/**
 * Tests for the comprehensive error handling system
 * Verifies error categorization, retry functionality, and user messaging
 */

import { describe, it, expect, jest } from '@jest/globals'
import {
  AppError,
  ErrorType,
  ErrorSeverity,
  parseApiError,
  withErrorHandling
} from '../lib/error-handling'

// Mock toast to avoid actual UI interactions during tests
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  }
}))

describe('Error Handling System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('AppError Class', () => {
    it('should create AppError with correct properties', () => {
      const error = new AppError(
        'Test error message',
        ErrorType.VALIDATION,
        ErrorSeverity.HIGH,
        'Custom user message',
        false,
        400
      )

      expect(error.message).toBe('Test error message')
      expect(error.type).toBe(ErrorType.VALIDATION)
      expect(error.severity).toBe(ErrorSeverity.HIGH)
      expect(error.userMessage).toBe('Custom user message')
      expect(error.retryable).toBe(false)
      expect(error.statusCode).toBe(400)
    })

    it('should use default user message when not provided', () => {
      const error = new AppError('Test error', ErrorType.AUTHENTICATION, ErrorSeverity.HIGH)
      expect(error.userMessage).toBe('Please sign in to continue.')
    })

    it('should set retryable correctly when specified', () => {
      const retryableError = new AppError('Network error', ErrorType.NETWORK, ErrorSeverity.MEDIUM, undefined, true)
      const nonRetryableError = new AppError('Validation error', ErrorType.VALIDATION, ErrorSeverity.LOW, undefined, false)

      expect(retryableError.retryable).toBe(true)
      expect(nonRetryableError.retryable).toBe(false)
    })
  })

  describe('parseApiError Function', () => {
    it('should parse fetch network errors correctly', () => {
      const fetchError = new TypeError('Failed to fetch')

      const appError = parseApiError(fetchError)

      expect(appError).toBeInstanceOf(AppError)
      expect(appError.type).toBe(ErrorType.NETWORK)
      expect(appError.severity).toBe(ErrorSeverity.HIGH)
      expect(appError.retryable).toBe(true)
    })

    it('should parse HTTP response errors correctly', () => {
      const httpError = {
        status: 404,
        statusText: 'Not Found',
        message: 'Resource not found'
      }

      const appError = parseApiError(httpError)

      expect(appError).toBeInstanceOf(AppError)
      expect(appError.type).toBe(ErrorType.NOT_FOUND)
      expect(appError.severity).toBe(ErrorSeverity.MEDIUM)
      expect(appError.retryable).toBe(false)
    })

    it('should parse validation errors correctly', () => {
      const validationError = {
        status: 400,
        message: 'Validation failed'
      }

      const appError = parseApiError(validationError)

      expect(appError).toBeInstanceOf(AppError)
      expect(appError.type).toBe(ErrorType.VALIDATION)
      expect(appError.severity).toBe(ErrorSeverity.MEDIUM)
      expect(appError.retryable).toBe(false)
    })

    it('should parse authentication errors correctly', () => {
      const authError = {
        status: 401,
        message: 'Unauthorized'
      }

      const appError = parseApiError(authError)

      expect(appError).toBeInstanceOf(AppError)
      expect(appError.type).toBe(ErrorType.AUTHENTICATION)
      expect(appError.severity).toBe(ErrorSeverity.HIGH)
      expect(appError.retryable).toBe(false)
    })

    it('should parse server errors correctly', () => {
      const serverError = {
        status: 500,
        message: 'Internal Server Error'
      }

      const appError = parseApiError(serverError)

      expect(appError).toBeInstanceOf(AppError)
      expect(appError.type).toBe(ErrorType.SERVER)
      expect(appError.severity).toBe(ErrorSeverity.HIGH)
      expect(appError.retryable).toBe(true)
    })

    it('should handle unknown errors gracefully', () => {
      const unknownError = 'Some string error'

      const appError = parseApiError(unknownError)

      expect(appError).toBeInstanceOf(AppError)
      expect(appError.type).toBe(ErrorType.UNKNOWN)
      expect(appError.severity).toBe(ErrorSeverity.MEDIUM)
      expect(appError.message).toBe('Unknown error occurred')
    })
  })

  describe('withErrorHandling Function', () => {
    it('should execute operation successfully and return result', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success result')
      
      const result = await withErrorHandling(
        mockOperation,
        'Test operation'
      )

      expect(result).toBe('success result')
      expect(mockOperation).toHaveBeenCalledTimes(1)
    })

    it('should handle errors and return null', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'))
      
      const result = await withErrorHandling(
        mockOperation,
        'Test operation'
      )

      expect(result).toBeNull()
      expect(mockOperation).toHaveBeenCalledTimes(1)
    })

    it('should provide retry function to error handler when provided', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'))
      const mockRetryFn = jest.fn().mockResolvedValue(undefined)

      const result = await withErrorHandling(
        mockOperation,
        'Test operation',
        mockRetryFn
      )

      expect(result).toBeNull()
      expect(mockOperation).toHaveBeenCalledTimes(1)
      // The retry function is not called automatically, only when user clicks retry button
      expect(mockRetryFn).toHaveBeenCalledTimes(0)
    })
  })

  describe('Error Type Classification', () => {
    it('should classify network errors correctly', () => {
      const networkErrors = [
        new TypeError('Failed to fetch'),
        new TypeError('fetch request failed')
      ]

      networkErrors.forEach(error => {
        const appError = parseApiError(error)
        expect(appError.type).toBe(ErrorType.NETWORK)
        expect(appError.retryable).toBe(true)
      })
    })

    it('should classify HTTP status codes correctly', () => {
      const testCases = [
        { status: 400, expectedType: ErrorType.VALIDATION },
        { status: 401, expectedType: ErrorType.AUTHENTICATION },
        { status: 403, expectedType: ErrorType.AUTHORIZATION },
        { status: 404, expectedType: ErrorType.NOT_FOUND },
        { status: 500, expectedType: ErrorType.SERVER },
        { status: 502, expectedType: ErrorType.SERVER },
        { status: 503, expectedType: ErrorType.SERVER }
      ]

      testCases.forEach(({ status, expectedType }) => {
        const error = { status, message: 'Test error' }
        const appError = parseApiError(error, 'Test')
        expect(appError.type).toBe(expectedType)
      })
    })
  })

  describe('Error Severity Levels', () => {
    it('should assign correct severity levels', () => {
      const testCases = [
        { type: ErrorType.VALIDATION, expectedSeverity: ErrorSeverity.LOW },
        { type: ErrorType.NOT_FOUND, expectedSeverity: ErrorSeverity.MEDIUM },
        { type: ErrorType.NETWORK, expectedSeverity: ErrorSeverity.HIGH },
        { type: ErrorType.SERVER, expectedSeverity: ErrorSeverity.CRITICAL },
        { type: ErrorType.AUTHENTICATION, expectedSeverity: ErrorSeverity.HIGH }
      ]

      testCases.forEach(({ type, expectedSeverity }) => {
        const error = new AppError('Test', type, expectedSeverity)
        expect(error.severity).toBe(expectedSeverity)
      })
    })
  })

  describe('Retry Logic', () => {
    it('should identify retryable errors correctly when explicitly set', () => {
      const retryableError = new AppError('Test', ErrorType.NETWORK, ErrorSeverity.MEDIUM, undefined, true)
      const nonRetryableError = new AppError('Test', ErrorType.VALIDATION, ErrorSeverity.MEDIUM, undefined, false)

      expect(retryableError.retryable).toBe(true)
      expect(nonRetryableError.retryable).toBe(false)
    })

    it('should create retryable errors through parseApiError for appropriate types', () => {
      const networkError = new TypeError('Failed to fetch')
      const serverError = { status: 500 }
      const validationError = { status: 400 }

      const parsedNetworkError = parseApiError(networkError)
      const parsedServerError = parseApiError(serverError)
      const parsedValidationError = parseApiError(validationError)

      expect(parsedNetworkError.retryable).toBe(true)
      expect(parsedServerError.retryable).toBe(true)
      expect(parsedValidationError.retryable).toBe(false)
    })
  })

  describe('User-Friendly Messages', () => {
    it('should generate appropriate user messages for different error types', () => {
      const testCases = [
        {
          type: ErrorType.NETWORK,
          expectedMessage: 'Network connection issue. Please check your internet connection and try again.'
        },
        {
          type: ErrorType.VALIDATION,
          expectedMessage: 'Please check your input and try again.'
        },
        {
          type: ErrorType.AUTHENTICATION,
          expectedMessage: 'Please sign in to continue.'
        },
        {
          type: ErrorType.NOT_FOUND,
          expectedMessage: 'The requested resource was not found.'
        },
        {
          type: ErrorType.SERVER,
          expectedMessage: 'Server error occurred. Please try again later.'
        }
      ]

      testCases.forEach(({ type, expectedMessage }) => {
        const error = new AppError('Original message', type, ErrorSeverity.MEDIUM)
        expect(error.userMessage).toBe(expectedMessage)
      })
    })
  })
})
