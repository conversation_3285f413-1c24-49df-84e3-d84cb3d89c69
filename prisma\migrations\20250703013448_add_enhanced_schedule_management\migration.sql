-- Create<PERSON><PERSON>
CREATE TYPE "TimeBlockType" AS ENUM ('PROCEDURE', 'SURGERY', 'ADMINISTRATIVE', 'PERSONAL', 'EMERGENCY_RESERVED', 'BREAK', 'LUNCH');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "TimeOffType" AS ENUM ('VACATION', 'SICK_LEAVE', 'PERSONAL', 'CONTINUING_EDUCATION', 'CONFERENCE', 'EMERGENCY');

-- CreateEnum
CREATE TYPE "TimeOffStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'CANCELLED');

-- CreateTable
CREATE TABLE "dentist_working_hours" (
    "id" TEXT NOT NULL,
    "dentistId" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "lunchStart" TEXT,
    "lunchEnd" TEXT,
    "breakSlots" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "effectiveFrom" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "effectiveTo" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dentist_working_hours_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dentist_time_blocks" (
    "id" TEXT NOT NULL,
    "dentistId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "blockType" "TimeBlockType" NOT NULL DEFAULT 'PROCEDURE',
    "isRecurring" BOOLEAN NOT NULL DEFAULT false,
    "recurringPattern" TEXT,
    "color" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dentist_time_blocks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dentist_time_off" (
    "id" TEXT NOT NULL,
    "dentistId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "timeOffType" "TimeOffType" NOT NULL DEFAULT 'VACATION',
    "status" "TimeOffStatus" NOT NULL DEFAULT 'PENDING',
    "reason" TEXT,
    "isAllDay" BOOLEAN NOT NULL DEFAULT true,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dentist_time_off_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dentist_schedule_templates" (
    "id" TEXT NOT NULL,
    "dentistId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "templateData" JSONB NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dentist_schedule_templates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "dentist_working_hours_dentistId_idx" ON "dentist_working_hours"("dentistId");

-- CreateIndex
CREATE INDEX "dentist_working_hours_effectiveFrom_idx" ON "dentist_working_hours"("effectiveFrom");

-- CreateIndex
CREATE UNIQUE INDEX "dentist_working_hours_dentistId_dayOfWeek_effectiveFrom_key" ON "dentist_working_hours"("dentistId", "dayOfWeek", "effectiveFrom");

-- CreateIndex
CREATE INDEX "dentist_time_blocks_dentistId_idx" ON "dentist_time_blocks"("dentistId");

-- CreateIndex
CREATE INDEX "dentist_time_blocks_startTime_idx" ON "dentist_time_blocks"("startTime");

-- CreateIndex
CREATE INDEX "dentist_time_blocks_blockType_idx" ON "dentist_time_blocks"("blockType");

-- CreateIndex
CREATE INDEX "dentist_time_off_dentistId_idx" ON "dentist_time_off"("dentistId");

-- CreateIndex
CREATE INDEX "dentist_time_off_startDate_idx" ON "dentist_time_off"("startDate");

-- CreateIndex
CREATE INDEX "dentist_time_off_status_idx" ON "dentist_time_off"("status");

-- CreateIndex
CREATE INDEX "dentist_schedule_templates_dentistId_idx" ON "dentist_schedule_templates"("dentistId");

-- CreateIndex
CREATE INDEX "dentist_schedule_templates_isDefault_idx" ON "dentist_schedule_templates"("isDefault");

-- AddForeignKey
ALTER TABLE "dentist_working_hours" ADD CONSTRAINT "dentist_working_hours_dentistId_fkey" FOREIGN KEY ("dentistId") REFERENCES "dentist_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dentist_time_blocks" ADD CONSTRAINT "dentist_time_blocks_dentistId_fkey" FOREIGN KEY ("dentistId") REFERENCES "dentist_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dentist_time_off" ADD CONSTRAINT "dentist_time_off_dentistId_fkey" FOREIGN KEY ("dentistId") REFERENCES "dentist_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dentist_schedule_templates" ADD CONSTRAINT "dentist_schedule_templates_dentistId_fkey" FOREIGN KEY ("dentistId") REFERENCES "dentist_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
