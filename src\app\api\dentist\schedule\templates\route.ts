import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { scheduleTemplateCreateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/dentist/schedule/templates - Get schedule templates for a dentist
export async function GET(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const { searchParams } = new URL(request.url)
      const dentistId = searchParams.get('dentistId')

      // Determine which dentist's templates to fetch
      let targetDentistId = dentistId
      if (!targetDentistId) {
        // If no dentistId provided, use current user's dentist profile
        if (user.role !== UserRole.DENTIST) {
          return NextResponse.json(
            { error: 'Dentist ID is required for non-dentist users' },
            { status: 400 }
          )
        }
        
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile) {
          return NextResponse.json(
            { error: 'Dentist profile not found' },
            { status: 404 }
          )
        }
        
        targetDentistId = dentistProfile.id
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== targetDentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to view these templates' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      const templates = await prisma.dentistScheduleTemplate.findMany({
        where: {
          dentistId: targetDentistId,
          isActive: true,
        },
        orderBy: [
          { isDefault: 'desc' },
          { name: 'asc' }
        ],
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: templates
      })

    } catch (error) {
      console.error('Error fetching schedule templates:', error)
      return NextResponse.json(
        { error: 'Failed to fetch schedule templates' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}

// POST /api/dentist/schedule/templates - Create schedule template
export async function POST(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const body = await request.json()
      const validatedData = scheduleTemplateCreateSchema.parse(body)

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== validatedData.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to create template for this dentist' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Verify dentist exists
      const dentist = await prisma.dentistProfile.findUnique({
        where: { id: validatedData.dentistId }
      })

      if (!dentist) {
        return NextResponse.json(
          { error: 'Dentist not found' },
          { status: 404 }
        )
      }

      // If this is being set as default, unset other default templates
      if (validatedData.isDefault) {
        await prisma.dentistScheduleTemplate.updateMany({
          where: {
            dentistId: validatedData.dentistId,
            isDefault: true,
          },
          data: {
            isDefault: false,
          }
        })
      }

      // Create schedule template
      const template = await prisma.dentistScheduleTemplate.create({
        data: {
          dentistId: validatedData.dentistId,
          name: validatedData.name,
          description: validatedData.description,
          templateData: validatedData.templateData,
          isDefault: validatedData.isDefault,
        },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: template
      }, { status: 201 })

    } catch (error) {
      console.error('Error creating schedule template:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create schedule template' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}
