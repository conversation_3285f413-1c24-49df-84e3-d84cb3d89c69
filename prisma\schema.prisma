// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// ENUMS
// ============================================================================

enum UserRole {
  PATIENT
  DENTIST
  STAFF
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum AppointmentType {
  CONSULTATION
  CLEANING
  FILLING
  EXTRACTION
  ROOT_CANAL
  CROWN
  BRIDGE
  IMPLANT
  ORTHODONTICS
  EMERGENCY
  FOLLOW_UP
}

enum PaymentStatus {
  PENDING
  PAID
  PARTIAL
  OVERDUE
  CANCELLED
  REFUNDED
}

enum PaymentMethod {
  CASH
  CREDIT_CARD
  DEBIT_CARD
  BANK_TRANSFER
  STRIPE
  PAYMONGO
  GCASH
  PAYMAYA
}

enum TreatmentStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum NotificationType {
  APPOINTMENT_REMINDER
  APPOINTMENT_CONFIRMATION
  PAYMENT_DUE
  TREATMENT_COMPLETE
  MARKETING
  SYSTEM
}

enum NotificationChannel {
  EMAIL
  SMS
  PUSH
  IN_APP
}

enum FileType {
  IMAGE
  PDF
  DOCUMENT
  XRAY
  PHOTO
}

enum ToothCondition {
  HEALTHY
  CAVITY
  FILLED
  CROWN
  BRIDGE
  IMPLANT
  MISSING
  ROOT_CANAL
  EXTRACTION_NEEDED
}

enum TimeBlockType {
  PROCEDURE
  SURGERY
  ADMINISTRATIVE
  PERSONAL
  EMERGENCY_RESERVED
  BREAK
  LUNCH
}

enum TimeOffType {
  VACATION
  SICK_LEAVE
  PERSONAL
  CONTINUING_EDUCATION
  CONFERENCE
  EMERGENCY
}

enum TimeOffStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

// ============================================================================
// AUTHENTICATION & USER MANAGEMENT
// ============================================================================

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model User {
  id            String     @id @default(cuid())
  email         String     @unique
  emailVerified DateTime?
  name          String?
  image         String?
  phone         String?
  password      String?    // For credentials-based authentication
  role          UserRole   @default(PATIENT)
  status        UserStatus @default(ACTIVE)
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // NextAuth.js relations
  accounts Account[]
  sessions Session[]

  // Role-specific profiles
  patientProfile PatientProfile?
  dentistProfile DentistProfile?
  staffProfile   StaffProfile?

  // Activity tracking
  auditLogs AuditLog[]

  // Communication
  notifications Notification[]

  // Content
  blogPosts BlogPost[]

  @@map("users")
}

// ============================================================================
// PROFILE MODELS
// ============================================================================

model PatientProfile {
  id                String    @id @default(cuid())
  userId            String    @unique
  dateOfBirth       DateTime?
  gender            String?
  address           String?
  emergencyContact  String?
  emergencyPhone    String?
  insuranceProvider String?
  insuranceNumber   String?
  medicalHistory    String?   @db.Text
  allergies         String?   @db.Text
  medications       String?   @db.Text
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointments Appointment[]
  treatments   Treatment[]
  invoices     Invoice[]
  dentalChart  DentalChart?
  files        PatientFile[]
  inventoryUsage InventoryUsage[]
  testimonials Testimonial[]

  @@map("patient_profiles")
}

model DentistProfile {
  id               String   @id @default(cuid())
  userId           String   @unique
  licenseNumber    String   @unique
  specialization   String?
  yearsExperience  Int?
  education        String?  @db.Text
  certifications   String?  @db.Text
  bio              String?  @db.Text
  consultationFee  Decimal? @db.Decimal(10, 2)
  isAvailable      Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  branches     BranchDentist[]
  appointments Appointment[]
  treatments   Treatment[]
  schedules    DentistSchedule[]
  workingHours DentistWorkingHours[]
  timeBlocks   DentistTimeBlock[]
  timeOff      DentistTimeOff[]
  scheduleTemplates DentistScheduleTemplate[]

  @@map("dentist_profiles")
}

model StaffProfile {
  id           String   @id @default(cuid())
  userId       String   @unique
  position     String
  department   String?
  hireDate     DateTime
  salary       Decimal? @db.Decimal(10, 2)
  permissions  String[] // JSON array of permissions
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user     User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  branches BranchStaff[]

  @@map("staff_profiles")
}

// ============================================================================
// CLINIC MANAGEMENT
// ============================================================================

model Branch {
  id          String   @id @default(cuid())
  name        String
  address     String
  phone       String?
  email       String?
  description String?  @db.Text
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  dentists     BranchDentist[]
  staff        BranchStaff[]
  appointments Appointment[]
  services     BranchService[]
  inventory    InventoryItem[]

  @@map("branches")
}

model BranchDentist {
  id        String   @id @default(cuid())
  branchId  String
  dentistId String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  branch  Branch         @relation(fields: [branchId], references: [id], onDelete: Cascade)
  dentist DentistProfile @relation(fields: [dentistId], references: [id], onDelete: Cascade)

  @@unique([branchId, dentistId])
  @@map("branch_dentists")
}

model BranchStaff {
  id        String   @id @default(cuid())
  branchId  String
  staffId   String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  branch Branch       @relation(fields: [branchId], references: [id], onDelete: Cascade)
  staff  StaffProfile @relation(fields: [staffId], references: [id], onDelete: Cascade)

  @@unique([branchId, staffId])
  @@map("branch_staff")
}

model Service {
  id          String  @id @default(cuid())
  name        String
  description String? @db.Text
  category    String
  duration    Int // in minutes
  price       Decimal @db.Decimal(10, 2)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  branches     BranchService[]
  appointments Appointment[]
  treatments   Treatment[]

  @@map("services")
}

model BranchService {
  id        String  @id @default(cuid())
  branchId  String
  serviceId String
  price     Decimal @db.Decimal(10, 2) // Branch-specific pricing
  isActive  Boolean @default(true)
  createdAt DateTime @default(now())

  branch  Branch  @relation(fields: [branchId], references: [id], onDelete: Cascade)
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@unique([branchId, serviceId])
  @@map("branch_services")
}

// ============================================================================
// APPOINTMENTS & SCHEDULING
// ============================================================================

model Appointment {
  id            String            @id @default(cuid())
  patientId     String
  dentistId     String
  branchId      String
  serviceId     String
  scheduledAt   DateTime
  duration      Int // in minutes
  status        AppointmentStatus @default(SCHEDULED)
  type          AppointmentType   @default(CONSULTATION)
  notes         String?           @db.Text
  symptoms      String?           @db.Text
  cancelReason  String?
  calendarEventId String?         // Google Calendar event ID for sync
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  patient  PatientProfile  @relation(fields: [patientId], references: [id], onDelete: Cascade)
  dentist  DentistProfile  @relation(fields: [dentistId], references: [id], onDelete: Cascade)
  branch   Branch          @relation(fields: [branchId], references: [id], onDelete: Cascade)
  service  Service         @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  treatments    Treatment[]
  notifications Notification[]

  @@index([patientId])
  @@index([dentistId])
  @@index([scheduledAt])
  @@map("appointments")
}

model DentistSchedule {
  id        String   @id @default(cuid())
  dentistId String
  dayOfWeek Int // 0 = Sunday, 1 = Monday, etc.
  startTime String // HH:MM format
  endTime   String // HH:MM format
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  dentist DentistProfile @relation(fields: [dentistId], references: [id], onDelete: Cascade)

  @@unique([dentistId, dayOfWeek])
  @@map("dentist_schedules")
}

// Enhanced working hours with break support
model DentistWorkingHours {
  id           String   @id @default(cuid())
  dentistId    String
  dayOfWeek    Int      // 0 = Sunday, 1 = Monday, etc.
  startTime    String   // HH:MM format
  endTime      String   // HH:MM format
  lunchStart   String?  // HH:MM format
  lunchEnd     String?  // HH:MM format
  breakSlots   Json?    // Array of break periods
  isActive     Boolean  @default(true)
  effectiveFrom DateTime @default(now())
  effectiveTo   DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  dentist DentistProfile @relation(fields: [dentistId], references: [id], onDelete: Cascade)

  @@unique([dentistId, dayOfWeek, effectiveFrom])
  @@index([dentistId])
  @@index([effectiveFrom])
  @@map("dentist_working_hours")
}

// Time blocking system
model DentistTimeBlock {
  id          String   @id @default(cuid())
  dentistId   String
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  blockType   TimeBlockType @default(PROCEDURE)
  isRecurring Boolean  @default(false)
  recurringPattern String? // JSON for recurring rules
  color       String?  // Hex color for visual distinction
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  dentist DentistProfile @relation(fields: [dentistId], references: [id], onDelete: Cascade)

  @@index([dentistId])
  @@index([startTime])
  @@index([blockType])
  @@map("dentist_time_blocks")
}

// Vacation and time-off management
model DentistTimeOff {
  id          String      @id @default(cuid())
  dentistId   String
  title       String
  startDate   DateTime
  endDate     DateTime
  timeOffType TimeOffType @default(VACATION)
  status      TimeOffStatus @default(PENDING)
  reason      String?
  isAllDay    Boolean     @default(true)
  approvedBy  String?
  approvedAt  DateTime?
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  dentist DentistProfile @relation(fields: [dentistId], references: [id], onDelete: Cascade)

  @@index([dentistId])
  @@index([startDate])
  @@index([status])
  @@map("dentist_time_off")
}

// Schedule templates for recurring patterns
model DentistScheduleTemplate {
  id          String   @id @default(cuid())
  dentistId   String
  name        String
  description String?
  templateData Json    // Complete schedule configuration
  isDefault   Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  dentist DentistProfile @relation(fields: [dentistId], references: [id], onDelete: Cascade)

  @@index([dentistId])
  @@index([isDefault])
  @@map("dentist_schedule_templates")
}

// ============================================================================
// TREATMENTS & MEDICAL RECORDS
// ============================================================================

model Treatment {
  id            String          @id @default(cuid())
  appointmentId String?
  patientId     String
  dentistId     String
  serviceId     String
  status        TreatmentStatus @default(PLANNED)
  diagnosis     String?         @db.Text
  procedure     String?         @db.Text
  notes         String?         @db.Text // SOAP format notes
  cost          Decimal         @db.Decimal(10, 2)
  startedAt     DateTime?
  completedAt   DateTime?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  appointment Appointment?    @relation(fields: [appointmentId], references: [id])
  patient     PatientProfile  @relation(fields: [patientId], references: [id], onDelete: Cascade)
  dentist     DentistProfile  @relation(fields: [dentistId], references: [id], onDelete: Cascade)
  service     Service         @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  prescriptions Prescription[]
  files         TreatmentFile[]
  invoiceItems  InvoiceItem[]

  @@index([patientId])
  @@index([dentistId])
  @@map("treatments")
}

model Prescription {
  id          String   @id @default(cuid())
  treatmentId String
  medication  String
  dosage      String
  frequency   String
  duration    String
  instructions String? @db.Text
  createdAt   DateTime @default(now())

  treatment Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  @@map("prescriptions")
}

model DentalChart {
  id        String   @id @default(cuid())
  patientId String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  patient PatientProfile @relation(fields: [patientId], references: [id], onDelete: Cascade)
  teeth   ToothRecord[]

  @@map("dental_charts")
}

model ToothRecord {
  id            String         @id @default(cuid())
  dentalChartId String
  toothNumber   Int // 1-32 for adult teeth
  condition     ToothCondition @default(HEALTHY)
  notes         String?        @db.Text
  lastUpdated   DateTime       @default(now())

  dentalChart DentalChart @relation(fields: [dentalChartId], references: [id], onDelete: Cascade)

  @@unique([dentalChartId, toothNumber])
  @@map("tooth_records")
}

// ============================================================================
// BILLING & PAYMENTS
// ============================================================================

model Invoice {
  id          String        @id @default(cuid())
  patientId   String
  invoiceNumber String      @unique
  totalAmount Decimal       @db.Decimal(10, 2)
  paidAmount  Decimal       @default(0) @db.Decimal(10, 2)
  status      PaymentStatus @default(PENDING)
  dueDate     DateTime
  issuedAt    DateTime      @default(now())
  paidAt      DateTime?
  notes       String?       @db.Text
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  patient  PatientProfile @relation(fields: [patientId], references: [id], onDelete: Cascade)
  items    InvoiceItem[]
  payments Payment[]

  @@index([patientId])
  @@index([status])
  @@map("invoices")
}

model InvoiceItem {
  id          String  @id @default(cuid())
  invoiceId   String
  treatmentId String?
  description String
  quantity    Int     @default(1)
  unitPrice   Decimal @db.Decimal(10, 2)
  totalPrice  Decimal @db.Decimal(10, 2)
  createdAt   DateTime @default(now())

  invoice   Invoice    @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  treatment Treatment? @relation(fields: [treatmentId], references: [id])

  @@map("invoice_items")
}

model Payment {
  id              String        @id @default(cuid())
  invoiceId       String
  amount          Decimal       @db.Decimal(10, 2)
  method          PaymentMethod
  transactionId   String?
  status          PaymentStatus @default(PENDING)
  processedAt     DateTime?
  notes           String?       @db.Text
  receiptUrl      String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
  @@index([status])
  @@map("payments")
}

// ============================================================================
// FILE MANAGEMENT
// ============================================================================

model PatientFile {
  id          String   @id @default(cuid())
  patientId   String
  fileName    String
  originalName String
  fileType    FileType
  fileSize    Int
  mimeType    String
  url         String
  description String?  @db.Text
  uploadedBy  String
  createdAt   DateTime @default(now())

  patient PatientProfile @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([patientId])
  @@map("patient_files")
}

model TreatmentFile {
  id          String   @id @default(cuid())
  treatmentId String
  fileName    String
  originalName String
  fileType    FileType
  fileSize    Int
  mimeType    String
  url         String
  description String?  @db.Text
  uploadedBy  String
  createdAt   DateTime @default(now())

  treatment Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  @@index([treatmentId])
  @@map("treatment_files")
}

// ============================================================================
// INVENTORY MANAGEMENT
// ============================================================================

model InventoryItem {
  id           String   @id @default(cuid())
  branchId     String
  name         String
  sku          String   @unique
  category     String
  description  String?  @db.Text
  supplier     String?
  unitPrice    Decimal? @db.Decimal(10, 2)
  currentStock Int      @default(0)
  minStock     Int      @default(0)
  maxStock     Int?
  unit         String // e.g., "pieces", "ml", "kg"
  expiryDate   DateTime?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  branch      Branch            @relation(fields: [branchId], references: [id], onDelete: Cascade)
  usageLogs   InventoryUsage[]
  stockLogs   StockMovement[]

  @@index([branchId])
  @@index([category])
  @@map("inventory_items")
}

model InventoryUsage {
  id          String   @id @default(cuid())
  itemId      String
  patientId   String?
  treatmentId String?
  quantity    Int
  usedBy      String
  notes       String?  @db.Text
  usedAt      DateTime @default(now())

  item    InventoryItem   @relation(fields: [itemId], references: [id], onDelete: Cascade)
  patient PatientProfile? @relation(fields: [patientId], references: [id])

  @@index([itemId])
  @@map("inventory_usage")
}

model StockMovement {
  id          String   @id @default(cuid())
  itemId      String
  type        String // "IN", "OUT", "ADJUSTMENT"
  quantity    Int
  reason      String
  reference   String? // Purchase order, invoice, etc.
  performedBy String
  notes       String?  @db.Text
  createdAt   DateTime @default(now())

  item InventoryItem @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@index([itemId])
  @@map("stock_movements")
}

// ============================================================================
// NOTIFICATIONS & COMMUNICATIONS
// ============================================================================

model Notification {
  id            String              @id @default(cuid())
  userId        String?
  appointmentId String?
  type          NotificationType
  channel       NotificationChannel
  title         String
  message       String              @db.Text
  isRead        Boolean             @default(false)
  sentAt        DateTime?
  scheduledFor  DateTime?
  metadata      Json?               // Additional data for the notification
  createdAt     DateTime            @default(now())

  user        User?        @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointment Appointment? @relation(fields: [appointmentId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
  @@index([scheduledFor])
  @@map("notifications")
}



// ============================================================================
// AUDIT & SECURITY
// ============================================================================

model AuditLog {
  id          String   @id @default(cuid())
  userId      String?
  action      String
  resource    String
  resourceId  String?
  oldValues   Json?
  newValues   Json?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([createdAt])
  @@map("audit_logs")
}

// ============================================================================
// SYSTEM CONFIGURATION
// ============================================================================

model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String   @db.Text
  description String?  @db.Text
  category    String   @default("general")
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([category])
  @@map("system_settings")
}

// ============================================================================
// MARKETING & CONTENT
// ============================================================================

model BlogPost {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  content     String   @db.Text
  excerpt     String?  @db.Text
  featuredImage String?
  authorId    String
  isPublished Boolean  @default(false)
  publishedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  author User @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@index([slug])
  @@index([isPublished])
  @@map("blog_posts")
}

model Testimonial {
  id          String   @id @default(cuid())
  patientId   String
  rating      Int // 1-5 stars
  title       String?
  content     String   @db.Text
  isApproved  Boolean  @default(false)
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  patient PatientProfile @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([patientId])
  @@index([isApproved])
  @@index([isPublic])
  @@map("testimonials")
}

model Newsletter {
  id          String   @id @default(cuid())
  email       String   @unique
  firstName   String?
  lastName    String?
  isActive    Boolean  @default(true)
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@index([email])
  @@index([isActive])
  @@map("newsletter_subscribers")
}
