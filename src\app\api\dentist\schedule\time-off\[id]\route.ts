import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { timeOffUpdateSchema } from '@/lib/validations'
import { UserRole, TimeOffStatus } from '@prisma/client'

// GET /api/dentist/schedule/time-off/[id] - Get specific time off request
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
    try {
      const timeOffRequest = await prisma.dentistTimeOff.findUnique({
        where: { id: id },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      if (!timeOffRequest) {
        return NextResponse.json(
          { error: 'Time off request not found' },
          { status: 404 }
        )
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== timeOffRequest.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to view this time off request' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      return NextResponse.json({
        success: true,
        data: timeOffRequest
      })

    } catch (error) {
      console.error('Error fetching time off request:', error)
      return NextResponse.json(
        { error: 'Failed to fetch time off request' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])

// PUT /api/dentist/schedule/time-off/[id] - Update time off request
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
    try {
      const body = await req.json()
      const validatedData = timeOffUpdateSchema.parse(body)

      // Check if time off request exists
      const existingTimeOff = await prisma.dentistTimeOff.findUnique({
        where: { id: id }
      })

      if (!existingTimeOff) {
        return NextResponse.json(
          { error: 'Time off request not found' },
          { status: 404 }
        )
      }

      // Authorization check
      let canModify = false
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (dentistProfile && dentistProfile.id === existingTimeOff.dentistId) {
          // Dentists can only modify their own pending requests
          canModify = existingTimeOff.status === TimeOffStatus.PENDING
        }
      } else if ([UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        // Admin/Staff can modify any request
        canModify = true
      }

      if (!canModify) {
        return NextResponse.json(
          { error: 'Unauthorized to modify this time off request' },
          { status: 403 }
        )
      }

      // Check for overlapping requests if dates are being changed
      if (validatedData.startDate || validatedData.endDate) {
        const newStartDate = validatedData.startDate || existingTimeOff.startDate
        const newEndDate = validatedData.endDate || existingTimeOff.endDate

        const overlappingRequests = await prisma.dentistTimeOff.findMany({
          where: {
            dentistId: existingTimeOff.dentistId,
            id: { not: id }, // Exclude current request
            status: { in: [TimeOffStatus.PENDING, TimeOffStatus.APPROVED] },
            OR: [
              {
                startDate: {
                  lte: newEndDate
                },
                endDate: {
                  gte: newStartDate
                }
              }
            ]
          }
        })

        if (overlappingRequests.length > 0) {
          return NextResponse.json(
            { error: 'Time off request overlaps with existing approved or pending requests' },
            { status: 409 }
          )
        }
      }

      // Prepare update data
      const updateData: any = { ...validatedData }

      // Handle status changes
      if (validatedData.status && [UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        if (validatedData.status === TimeOffStatus.APPROVED) {
          updateData.approvedBy = user.id
          updateData.approvedAt = new Date()
        } else if (validatedData.status === TimeOffStatus.REJECTED) {
          updateData.approvedBy = user.id
          updateData.approvedAt = new Date()
        }
      }

      updateData.updatedAt = new Date()

      // Update time off request
      const updatedTimeOff = await prisma.dentistTimeOff.update({
        where: { id: id },
        data: updateData,
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: updatedTimeOff
      })

    } catch (error) {
      console.error('Error updating time off request:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to update time off request' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])

// DELETE /api/dentist/schedule/time-off/[id] - Delete time off request
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
    try {
      // Check if time off request exists
      const existingTimeOff = await prisma.dentistTimeOff.findUnique({
        where: { id: id }
      })

      if (!existingTimeOff) {
        return NextResponse.json(
          { error: 'Time off request not found' },
          { status: 404 }
        )
      }

      // Authorization check
      let canDelete = false
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (dentistProfile && dentistProfile.id === existingTimeOff.dentistId) {
          // Dentists can only delete their own pending requests
          canDelete = existingTimeOff.status === TimeOffStatus.PENDING
        }
      } else if ([UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        // Admin/Staff can delete any request
        canDelete = true
      }

      if (!canDelete) {
        return NextResponse.json(
          { error: 'Unauthorized to delete this time off request' },
          { status: 403 }
        )
      }

      // Delete time off request
      await prisma.dentistTimeOff.delete({
        where: { id: id }
      })

      return NextResponse.json({
        success: true,
        message: 'Time off request deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting time off request:', error)
      return NextResponse.json(
        { error: 'Failed to delete time off request' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])
