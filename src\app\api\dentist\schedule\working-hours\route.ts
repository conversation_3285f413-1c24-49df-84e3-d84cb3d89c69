import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { workingHoursCreateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/dentist/schedule/working-hours - Get working hours for a dentist
export async function GET(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const { searchParams } = new URL(request.url)
      const dentistId = searchParams.get('dentistId')
      const effectiveDate = searchParams.get('effectiveDate')

      // Determine which dentist's schedule to fetch
      let targetDentistId = dentistId
      if (!targetDentistId) {
        // If no dentistId provided, use current user's dentist profile
        if (user.role !== UserRole.DENTIST) {
          return NextResponse.json(
            { error: 'Dentist ID is required for non-dentist users' },
            { status: 400 }
          )
        }

        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile) {
          return NextResponse.json(
            { error: 'Dentist profile not found' },
            { status: 404 }
          )
        }
        
        targetDentistId = dentistProfile.id
      }

      // Authorization check - only allow dentists to view their own schedule or admins/staff to view any
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })

        if (!dentistProfile || dentistProfile.id !== targetDentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to view this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Build query conditions
      const whereConditions: any = {
        dentistId: targetDentistId,
        isActive: true,
      }

      if (effectiveDate) {
        const date = new Date(effectiveDate)
        whereConditions.effectiveFrom = { lte: date }
        whereConditions.OR = [
          { effectiveTo: null },
          { effectiveTo: { gte: date } }
        ]
      }

      const workingHours = await prisma.dentistWorkingHours.findMany({
        where: whereConditions,
        orderBy: [
          { dayOfWeek: 'asc' },
          { effectiveFrom: 'desc' }
        ],
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: workingHours
      })

    } catch (error) {
      console.error('Error fetching working hours:', error)
      return NextResponse.json(
        { error: 'Failed to fetch working hours' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}

// POST /api/dentist/schedule/working-hours - Create or update working hours
export async function POST(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const body = await request.json()
      const validatedData = workingHoursCreateSchema.parse(body)

      // Authorization check - only allow dentists to modify their own schedule or admins/staff
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })

        if (!dentistProfile || dentistProfile.id !== validatedData.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to modify this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Verify dentist exists
      const dentist = await prisma.dentistProfile.findUnique({
        where: { id: validatedData.dentistId }
      })

      if (!dentist) {
        return NextResponse.json(
          { error: 'Dentist not found' },
          { status: 404 }
        )
      }

      // Check for existing working hours for the same day and effective date
      const existingWorkingHours = await prisma.dentistWorkingHours.findFirst({
        where: {
          dentistId: validatedData.dentistId,
          dayOfWeek: validatedData.dayOfWeek,
          effectiveFrom: validatedData.effectiveFrom || new Date(),
        }
      })

      if (existingWorkingHours) {
        return NextResponse.json(
          { error: 'Working hours already exist for this day and effective date' },
          { status: 409 }
        )
      }

      // Create new working hours
      const workingHours = await prisma.dentistWorkingHours.create({
        data: {
          dentistId: validatedData.dentistId,
          dayOfWeek: validatedData.dayOfWeek,
          startTime: validatedData.startTime,
          endTime: validatedData.endTime,
          lunchStart: validatedData.lunchStart,
          lunchEnd: validatedData.lunchEnd,
          breakSlots: validatedData.breakSlots || [],
          effectiveFrom: validatedData.effectiveFrom || new Date(),
          effectiveTo: validatedData.effectiveTo,
        },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: workingHours
      }, { status: 201 })

    } catch (error) {
      console.error('Error creating working hours:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create working hours' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}
