import { useState, useCallback } from 'react'
import { toast } from 'sonner'
import type {
  WorkingHoursFormData,
  TimeBlockFormData,
  TimeOffFormData,
  ScheduleTemplateFormData,
  BulkUpdateFormData
} from '@/lib/validations/schedule'
import type {
  WorkingHoursCreateInput
} from '@/lib/validations'

// Types for API responses
interface APIResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

interface WorkingHour {
  id: string
  dentistId: string
  dayOfWeek: number
  startTime: string
  endTime: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

interface TimeBlock {
  id: string
  dentistId: string
  title: string
  startTime: string
  endTime: string
  date: Date
  type: 'procedure' | 'break' | 'personal' | 'emergency' | 'admin'
  description?: string
  isRecurring: boolean
  createdAt: Date
  updatedAt: Date
}

interface TimeOff {
  id: string
  dentistId: string
  startDate: Date
  endDate: Date
  type: 'vacation' | 'sick' | 'personal' | 'conference' | 'emergency'
  title: string
  description?: string
  isAllDay: boolean
  startTime?: string
  endTime?: string
  createdAt: Date
  updatedAt: Date
}

interface ScheduleTemplate {
  id: string
  dentistId: string
  name: string
  description?: string
  workingHours: WorkingHour[]
  timeBlocks?: TimeBlock[]
  isDefault: boolean
  createdAt: Date
  updatedAt: Date
}

// Custom hook for working hours management
export function useWorkingHours() {
  const [data, setData] = useState<WorkingHour[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchWorkingHours = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/working-hours')

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          throw new Error('Authentication required. Please sign in.')
        }
        if (response.status === 403) {
          throw new Error('Access denied. You must be a dentist to view this data.')
        }
        if (response.status === 404) {
          throw new Error('Dentist profile not found. Please contact support.')
        }

        const result: APIResponse<WorkingHour[]> = await response.json()
        throw new Error(result.error || 'Failed to fetch working hours')
      }

      const result: APIResponse<WorkingHour[]> = await response.json()
      setData(result.data || [])
      return result.data || []
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)

      // Only show toast for non-authentication errors to avoid spam
      if (!errorMessage.includes('Authentication') && !errorMessage.includes('Access denied')) {
        toast.error(`Failed to load working hours: ${errorMessage}`)
      }

      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const createWorkingHour = useCallback(async (workingHour: WorkingHoursCreateInput) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/working-hours', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workingHour)
      })
      const result: APIResponse<WorkingHour> = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to create working hour')
      }
      
      if (result.data) {
        setData(prev => [...prev, result.data!])
        toast.success('Working hours saved successfully')
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to save working hours: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const updateWorkingHour = useCallback(async (id: string, updates: Partial<WorkingHoursFormData>) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/dentist/schedule/working-hours/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })
      const result: APIResponse<WorkingHour> = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to update working hour')
      }
      
      if (result.data) {
        setData(prev => prev.map(item => item.id === id ? result.data! : item))
        toast.success('Working hours updated successfully')
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to update working hours: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteWorkingHour = useCallback(async (id: string) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/dentist/schedule/working-hours/${id}`, {
        method: 'DELETE'
      })
      const result: APIResponse<void> = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete working hour')
      }
      
      setData(prev => prev.filter(item => item.id !== id))
      toast.success('Working hours deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to delete working hours: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    data,
    loading,
    error,
    fetchWorkingHours,
    createWorkingHour,
    updateWorkingHour,
    deleteWorkingHour,
    clearError: () => setError(null)
  }
}

// Custom hook for time blocks management
export function useTimeBlocks() {
  const [data, setData] = useState<TimeBlock[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTimeBlocks = useCallback(async (date?: Date) => {
    setLoading(true)
    setError(null)
    try {
      const url = date
        ? `/api/dentist/schedule/time-blocks?date=${date.toISOString()}`
        : '/api/dentist/schedule/time-blocks'

      const response = await fetch(url)

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          throw new Error('Authentication required. Please sign in.')
        }
        if (response.status === 403) {
          throw new Error('Access denied. You must be a dentist to view this data.')
        }
        if (response.status === 404) {
          throw new Error('Dentist profile not found. Please contact support.')
        }

        const result: APIResponse<TimeBlock[]> = await response.json()
        throw new Error(result.error || 'Failed to fetch time blocks')
      }

      const result: APIResponse<TimeBlock[]> = await response.json()
      setData(result.data || [])
      return result.data || []
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)

      // Only show toast for non-authentication errors to avoid spam
      if (!errorMessage.includes('Authentication') && !errorMessage.includes('Access denied')) {
        toast.error(`Failed to load time blocks: ${errorMessage}`)
      }

      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const createTimeBlock = useCallback(async (timeBlock: TimeBlockFormData) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/time-blocks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(timeBlock)
      })
      const result: APIResponse<TimeBlock> = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to create time block')
      }
      
      if (result.data) {
        setData(prev => [...prev, result.data!])
        toast.success('Time block created successfully')
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to create time block: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const updateTimeBlock = useCallback(async (id: string, updates: Partial<TimeBlockFormData>) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/dentist/schedule/time-blocks/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })
      const result: APIResponse<TimeBlock> = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to update time block')
      }
      
      if (result.data) {
        setData(prev => prev.map(item => item.id === id ? result.data! : item))
        toast.success('Time block updated successfully')
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to update time block: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteTimeBlock = useCallback(async (id: string) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/dentist/schedule/time-blocks/${id}`, {
        method: 'DELETE'
      })
      const result: APIResponse<void> = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete time block')
      }
      
      setData(prev => prev.filter(item => item.id !== id))
      toast.success('Time block deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to delete time block: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    data,
    loading,
    error,
    fetchTimeBlocks,
    createTimeBlock,
    updateTimeBlock,
    deleteTimeBlock,
    clearError: () => setError(null)
  }
}

// Custom hook for time off management
export function useTimeOff() {
  const [data, setData] = useState<TimeOff[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTimeOff = useCallback(async (startDate?: Date, endDate?: Date) => {
    setLoading(true)
    setError(null)
    try {
      let url = '/api/dentist/schedule/time-off'
      const params = new URLSearchParams()

      if (startDate) params.append('startDate', startDate.toISOString())
      if (endDate) params.append('endDate', endDate.toISOString())

      if (params.toString()) {
        url += `?${params.toString()}`
      }

      const response = await fetch(url)

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          throw new Error('Authentication required. Please sign in.')
        }
        if (response.status === 403) {
          throw new Error('Access denied. You must be a dentist to view this data.')
        }
        if (response.status === 404) {
          throw new Error('Dentist profile not found. Please contact support.')
        }

        const result: APIResponse<TimeOff[]> = await response.json()
        throw new Error(result.error || 'Failed to fetch time off')
      }

      const result: APIResponse<TimeOff[]> = await response.json()
      setData(result.data || [])
      return result.data || []
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)

      // Only show toast for non-authentication errors to avoid spam
      if (!errorMessage.includes('Authentication') && !errorMessage.includes('Access denied')) {
        toast.error(`Failed to load time off: ${errorMessage}`)
      }

      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const createTimeOff = useCallback(async (timeOff: TimeOffFormData) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/time-off', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(timeOff)
      })
      const result: APIResponse<TimeOff> = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create time off')
      }

      if (result.data) {
        setData(prev => [...prev, result.data!])
        toast.success('Time off created successfully')
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to create time off: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const updateTimeOff = useCallback(async (id: string, updates: Partial<TimeOffFormData>) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/dentist/schedule/time-off/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })
      const result: APIResponse<TimeOff> = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update time off')
      }

      if (result.data) {
        setData(prev => prev.map(item => item.id === id ? result.data! : item))
        toast.success('Time off updated successfully')
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to update time off: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteTimeOff = useCallback(async (id: string) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/dentist/schedule/time-off/${id}`, {
        method: 'DELETE'
      })
      const result: APIResponse<void> = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete time off')
      }

      setData(prev => prev.filter(item => item.id !== id))
      toast.success('Time off deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to delete time off: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    data,
    loading,
    error,
    fetchTimeOff,
    createTimeOff,
    updateTimeOff,
    deleteTimeOff,
    clearError: () => setError(null)
  }
}

// Custom hook for schedule templates management
export function useScheduleTemplates() {
  const [data, setData] = useState<ScheduleTemplate[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTemplates = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/templates')

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          throw new Error('Authentication required. Please sign in.')
        }
        if (response.status === 403) {
          throw new Error('Access denied. You must be a dentist to view this data.')
        }
        if (response.status === 404) {
          throw new Error('Dentist profile not found. Please contact support.')
        }

        const result: APIResponse<ScheduleTemplate[]> = await response.json()
        throw new Error(result.error || 'Failed to fetch templates')
      }

      const result: APIResponse<ScheduleTemplate[]> = await response.json()
      setData(result.data || [])
      return result.data || []
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)

      // Only show toast for non-authentication errors to avoid spam
      if (!errorMessage.includes('Authentication') && !errorMessage.includes('Access denied')) {
        toast.error(`Failed to load templates: ${errorMessage}`)
      }

      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const createTemplate = useCallback(async (template: ScheduleTemplateFormData) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(template)
      })
      const result: APIResponse<ScheduleTemplate> = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create template')
      }

      if (result.data) {
        setData(prev => [...prev, result.data!])
        toast.success('Schedule template created successfully')
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to create template: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    data,
    loading,
    error,
    fetchTemplates,
    createTemplate,
    clearError: () => setError(null)
  }
}

// Custom hook for availability checking
export function useAvailability() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const checkAvailability = useCallback(async (date: Date, startTime: string, endTime: string) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date, startTime, endTime })
      })
      const result: APIResponse<{ available: boolean; conflicts?: any[] }> = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to check availability')
      }

      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Failed to check availability: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    loading,
    error,
    checkAvailability,
    clearError: () => setError(null)
  }
}

// Custom hook for bulk operations
export function useBulkOperations() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const bulkUpdate = useCallback(async (bulkData: BulkUpdateFormData) => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/dentist/schedule/bulk-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(bulkData)
      })
      const result: APIResponse<{ updated: number; errors?: any[] }> = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to perform bulk update')
      }

      if (result.data) {
        toast.success(`Bulk operation completed: ${result.data.updated} items updated`)
      }
      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      toast.error(`Bulk operation failed: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    loading,
    error,
    bulkUpdate,
    clearError: () => setError(null)
  }
}
