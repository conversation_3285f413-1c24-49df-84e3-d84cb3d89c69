import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { timeBlockCreateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/dentist/schedule/time-blocks - Get time blocks for a dentist
export async function GET(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const { searchParams } = new URL(request.url)
      const dentistId = searchParams.get('dentistId')
      const startDate = searchParams.get('startDate')
      const endDate = searchParams.get('endDate')
      const blockType = searchParams.get('blockType')

      // Determine which dentist's time blocks to fetch
      let targetDentistId = dentistId
      if (!targetDentistId) {
        // If no dentistId provided, use current user's dentist profile
        if (user.role !== UserRole.DENTIST) {
          return NextResponse.json(
            { error: 'Dentist ID is required for non-dentist users' },
            { status: 400 }
          )
        }
        
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile) {
          return NextResponse.json(
            { error: 'Dentist profile not found' },
            { status: 404 }
          )
        }
        
        targetDentistId = dentistProfile.id
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== targetDentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to view this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Build query conditions
      const whereConditions: any = {
        dentistId: targetDentistId,
        isActive: true,
      }

      if (startDate && endDate) {
        whereConditions.startTime = {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      }

      if (blockType) {
        whereConditions.blockType = blockType
      }

      const timeBlocks = await prisma.dentistTimeBlock.findMany({
        where: whereConditions,
        orderBy: { startTime: 'asc' },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: timeBlocks
      })

    } catch (error) {
      console.error('Error fetching time blocks:', error)
      return NextResponse.json(
        { error: 'Failed to fetch time blocks' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}

// POST /api/dentist/schedule/time-blocks - Create time block
export async function POST(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const body = await request.json()
      const validatedData = timeBlockCreateSchema.parse(body)

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== validatedData.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to modify this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Verify dentist exists
      const dentist = await prisma.dentistProfile.findUnique({
        where: { id: validatedData.dentistId }
      })

      if (!dentist) {
        return NextResponse.json(
          { error: 'Dentist not found' },
          { status: 404 }
        )
      }

      // Check for overlapping time blocks
      const overlappingBlocks = await prisma.dentistTimeBlock.findMany({
        where: {
          dentistId: validatedData.dentistId,
          isActive: true,
          OR: [
            {
              startTime: {
                lt: validatedData.endTime
              },
              endTime: {
                gt: validatedData.startTime
              }
            }
          ]
        }
      })

      if (overlappingBlocks.length > 0) {
        return NextResponse.json(
          { error: 'Time block overlaps with existing blocks' },
          { status: 409 }
        )
      }

      // Create time block
      const timeBlock = await prisma.dentistTimeBlock.create({
        data: {
          dentistId: validatedData.dentistId,
          title: validatedData.title,
          description: validatedData.description,
          startTime: validatedData.startTime,
          endTime: validatedData.endTime,
          blockType: validatedData.blockType,
          isRecurring: validatedData.isRecurring,
          recurringPattern: validatedData.recurringPattern,
          color: validatedData.color,
        },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: timeBlock
      }, { status: 201 })

    } catch (error) {
      console.error('Error creating time block:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create time block' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}
