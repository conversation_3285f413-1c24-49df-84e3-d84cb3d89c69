import { z } from 'zod'

// Time format validation regex (HH:MM format)
const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/

// Base time slot schema
export const timeSlotSchema = z.object({
  startTime: z.string().regex(timeRegex, 'Invalid time format (HH:MM)'),
  endTime: z.string().regex(timeRegex, 'Invalid time format (HH:MM)'),
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  description: z.string().optional()
}).refine(data => {
  const start = new Date(`2000-01-01T${data.startTime}:00`)
  const end = new Date(`2000-01-01T${data.endTime}:00`)
  return end > start
}, {
  message: 'End time must be after start time',
  path: ['endTime']
})

// Working hours schema
export const workingHoursSchema = z.object({
  dayOfWeek: z.number().min(0).max(6),
  startTime: z.string().regex(timeRegex, 'Invalid start time format'),
  endTime: z.string().regex(timeRegex, 'Invalid end time format'),
  isActive: z.boolean().default(true)
}).refine(data => {
  const start = new Date(`2000-01-01T${data.startTime}:00`)
  const end = new Date(`2000-01-01T${data.endTime}:00`)
  return end > start
}, {
  message: 'End time must be after start time',
  path: ['endTime']
}).refine(data => {
  const start = new Date(`2000-01-01T${data.startTime}:00`)
  const end = new Date(`2000-01-01T${data.endTime}:00`)
  const diffHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60)
  return diffHours <= 12
}, {
  message: 'Working hours cannot exceed 12 hours per day',
  path: ['endTime']
})

// Time block schema
export const timeBlockSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  startTime: z.string().regex(timeRegex, 'Invalid start time format'),
  endTime: z.string().regex(timeRegex, 'Invalid end time format'),
  date: z.date(),
  type: z.enum(['procedure', 'break', 'personal', 'emergency', 'admin'], {
    errorMap: () => ({ message: 'Invalid time block type' })
  }),
  description: z.string().max(500, 'Description too long').optional(),
  isRecurring: z.boolean().default(false)
}).refine(data => {
  const start = new Date(`2000-01-01T${data.startTime}:00`)
  const end = new Date(`2000-01-01T${data.endTime}:00`)
  return end > start
}, {
  message: 'End time must be after start time',
  path: ['endTime']
}).refine(data => {
  const start = new Date(`2000-01-01T${data.startTime}:00`)
  const end = new Date(`2000-01-01T${data.endTime}:00`)
  const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60)
  return diffMinutes >= 15
}, {
  message: 'Time block must be at least 15 minutes',
  path: ['endTime']
})

// Holiday schema
export const holidaySchema = z.object({
  name: z.string().min(1, 'Holiday name is required').max(100, 'Name too long'),
  date: z.date(),
  type: z.enum(['national', 'clinic', 'personal'], {
    errorMap: () => ({ message: 'Invalid holiday type' })
  }),
  description: z.string().max(500, 'Description too long').optional(),
  isRecurring: z.boolean().default(false)
})

// Recurring pattern schema
export const recurringPatternSchema = z.object({
  type: z.enum(['daily', 'weekly', 'monthly', 'custom'], {
    errorMap: () => ({ message: 'Invalid pattern type' })
  }),
  interval: z.number().min(1, 'Interval must be at least 1').max(365, 'Interval too large'),
  daysOfWeek: z.array(z.number().min(0).max(6)).optional(),
  dayOfMonth: z.number().min(1).max(31).optional(),
  endDate: z.date().optional(),
  occurrences: z.number().min(1).max(1000).optional(),
  title: z.string().min(1, 'Pattern title is required').max(100, 'Title too long'),
  startTime: z.string().regex(timeRegex, 'Invalid start time format'),
  endTime: z.string().regex(timeRegex, 'Invalid end time format')
}).refine(data => {
  if (data.type === 'weekly' && (!data.daysOfWeek || data.daysOfWeek.length === 0)) {
    return false
  }
  return true
}, {
  message: 'Weekly patterns must specify days of week',
  path: ['daysOfWeek']
}).refine(data => {
  if (data.type === 'monthly' && !data.dayOfMonth) {
    return false
  }
  return true
}, {
  message: 'Monthly patterns must specify day of month',
  path: ['dayOfMonth']
}).refine(data => {
  const start = new Date(`2000-01-01T${data.startTime}:00`)
  const end = new Date(`2000-01-01T${data.endTime}:00`)
  return end > start
}, {
  message: 'End time must be after start time',
  path: ['endTime']
})

// Time off schema
export const timeOffSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
  type: z.enum(['vacation', 'sick', 'personal', 'conference', 'emergency'], {
    errorMap: () => ({ message: 'Invalid time off type' })
  }),
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  description: z.string().max(500, 'Description too long').optional(),
  isAllDay: z.boolean().default(true),
  startTime: z.string().regex(timeRegex, 'Invalid start time format').optional(),
  endTime: z.string().regex(timeRegex, 'Invalid end time format').optional()
}).refine(data => {
  return data.endDate >= data.startDate
}, {
  message: 'End date must be on or after start date',
  path: ['endDate']
}).refine(data => {
  if (!data.isAllDay && (!data.startTime || !data.endTime)) {
    return false
  }
  return true
}, {
  message: 'Start and end times are required for partial day time off',
  path: ['startTime']
}).refine(data => {
  if (!data.isAllDay && data.startTime && data.endTime) {
    const start = new Date(`2000-01-01T${data.startTime}:00`)
    const end = new Date(`2000-01-01T${data.endTime}:00`)
    return end > start
  }
  return true
}, {
  message: 'End time must be after start time',
  path: ['endTime']
})

// Schedule template schema
export const scheduleTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  workingHours: z.array(workingHoursSchema),
  timeBlocks: z.array(timeBlockSchema).optional(),
  isDefault: z.boolean().default(false)
}).refine(data => {
  // Ensure at least one working day is defined
  return data.workingHours.some(wh => wh.isActive)
}, {
  message: 'Template must have at least one active working day',
  path: ['workingHours']
})

// Bulk update schema
export const bulkUpdateSchema = z.object({
  operation: z.enum(['create', 'update', 'delete'], {
    errorMap: () => ({ message: 'Invalid bulk operation' })
  }),
  type: z.enum(['working-hours', 'time-blocks', 'time-off'], {
    errorMap: () => ({ message: 'Invalid bulk update type' })
  }),
  data: z.array(z.any()).min(1, 'At least one item required for bulk operation'),
  dateRange: z.object({
    startDate: z.date(),
    endDate: z.date()
  }).optional()
})

// Form data types (inferred from schemas)
export type TimeSlotFormData = z.infer<typeof timeSlotSchema>
export type WorkingHoursFormData = z.infer<typeof workingHoursSchema>
export type TimeBlockFormData = z.infer<typeof timeBlockSchema>
export type HolidayFormData = z.infer<typeof holidaySchema>
export type RecurringPatternFormData = z.infer<typeof recurringPatternSchema>
export type TimeOffFormData = z.infer<typeof timeOffSchema>
export type ScheduleTemplateFormData = z.infer<typeof scheduleTemplateSchema>
export type BulkUpdateFormData = z.infer<typeof bulkUpdateSchema>

// Validation helper functions
export const validateTimeSlot = (data: unknown) => timeSlotSchema.safeParse(data)
export const validateWorkingHours = (data: unknown) => workingHoursSchema.safeParse(data)
export const validateTimeBlock = (data: unknown) => timeBlockSchema.safeParse(data)
export const validateHoliday = (data: unknown) => holidaySchema.safeParse(data)
export const validateRecurringPattern = (data: unknown) => recurringPatternSchema.safeParse(data)
export const validateTimeOff = (data: unknown) => timeOffSchema.safeParse(data)
export const validateScheduleTemplate = (data: unknown) => scheduleTemplateSchema.safeParse(data)
export const validateBulkUpdate = (data: unknown) => bulkUpdateSchema.safeParse(data)

// Time validation utilities
export const isValidTimeFormat = (time: string): boolean => timeRegex.test(time)

export const isTimeAfter = (time1: string, time2: string): boolean => {
  const t1 = new Date(`2000-01-01T${time1}:00`)
  const t2 = new Date(`2000-01-01T${time2}:00`)
  return t1 > t2
}

export const getTimeDifferenceMinutes = (startTime: string, endTime: string): number => {
  const start = new Date(`2000-01-01T${startTime}:00`)
  const end = new Date(`2000-01-01T${endTime}:00`)
  return (end.getTime() - start.getTime()) / (1000 * 60)
}

export const formatTimeForDisplay = (time: string): string => {
  const [hours, minutes] = time.split(':')
  const hour = parseInt(hours, 10)
  const ampm = hour >= 12 ? 'PM' : 'AM'
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
  return `${displayHour}:${minutes} ${ampm}`
}
