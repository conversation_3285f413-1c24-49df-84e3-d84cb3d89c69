'use client'

import * as React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import { Calendar, Plus, ChevronLeft, ChevronRight } from 'lucide-react'

interface Holiday {
  id: string
  name: string
  date: Date
  type: 'national' | 'clinic' | 'personal'
  description?: string
  recurring?: boolean
}

interface HolidayCalendarProps {
  holidays?: Holiday[]
  onAddHoliday?: (holiday: Omit<Holiday, 'id'>) => void
  onEditHoliday?: (holiday: Holiday) => void
  onDeleteHoliday?: (holidayId: string) => void
  className?: string
  readOnly?: boolean
}

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
]

const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

export function HolidayCalendar({
  holidays = [],
  onAddHoliday,
  onEditHoliday,
  onDeleteHoliday: _onDeleteHoliday,
  className,
  readOnly = false
}: HolidayCalendarProps) {
  const [currentDate, setCurrentDate] = React.useState(new Date())
  const [isAddDialogOpen, setIsAddDialogOpen] = React.useState(false)
  const [editingHoliday, setEditingHoliday] = React.useState<Holiday | null>(null)
  const [newHoliday, setNewHoliday] = React.useState({
    name: '',
    date: new Date().toISOString().split('T')[0],
    type: 'clinic' as Holiday['type'],
    description: '',
    recurring: false
  })

  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth()

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay()
  }

  const getHolidaysForDate = (date: Date) => {
    return holidays.filter(holiday => {
      const holidayDate = new Date(holiday.date)
      return holidayDate.toDateString() === date.toDateString()
    })
  }

  const getHolidayTypeColor = (type: Holiday['type']) => {
    switch (type) {
      case 'national':
        return 'bg-red-100 text-red-800 border-red-300'
      case 'clinic':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'personal':
        return 'bg-green-100 text-green-800 border-green-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const handleAddHoliday = () => {
    if (newHoliday.name && newHoliday.date) {
      onAddHoliday?.({
        name: newHoliday.name,
        date: new Date(newHoliday.date),
        type: newHoliday.type,
        description: newHoliday.description,
        recurring: newHoliday.recurring
      })

      setNewHoliday({
        name: '',
        date: new Date().toISOString().split('T')[0],
        type: 'clinic',
        description: '',
        recurring: false
      })
      setIsAddDialogOpen(false)
    }
  }

  const handleEditHoliday = (holiday: Holiday) => {
    setEditingHoliday(holiday)
    setNewHoliday({
      name: holiday.name,
      date: holiday.date.toISOString().split('T')[0],
      type: holiday.type,
      description: holiday.description || '',
      recurring: holiday.recurring || false
    })
  }

  const handleUpdateHoliday = () => {
    if (!editingHoliday) return

    if (newHoliday.name && newHoliday.date) {
      onEditHoliday?.({
        ...editingHoliday,
        name: newHoliday.name,
        date: new Date(newHoliday.date),
        type: newHoliday.type,
        description: newHoliday.description,
        recurring: newHoliday.recurring
      })

      setEditingHoliday(null)
      setNewHoliday({
        name: '',
        date: new Date().toISOString().split('T')[0],
        type: 'clinic',
        description: '',
        recurring: false
      })
    }
  }

  // Generate calendar grid
  const daysInMonth = getDaysInMonth(currentYear, currentMonth)
  const firstDayWeekday = getFirstDayOfMonth(currentYear, currentMonth)
  const daysInPrevMonth = getDaysInMonth(currentYear, currentMonth - 1)

  const calendarDays = []
  
  // Previous month's trailing days
  for (let i = firstDayWeekday - 1; i >= 0; i--) {
    const day = daysInPrevMonth - i
    calendarDays.push({
      day,
      isCurrentMonth: false,
      isPrevMonth: true,
      date: new Date(currentYear, currentMonth - 1, day)
    })
  }
  
  // Current month's days
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: true,
      isPrevMonth: false,
      date: new Date(currentYear, currentMonth, day)
    })
  }
  
  // Next month's leading days
  const remainingCells = 42 - calendarDays.length
  for (let day = 1; day <= remainingCells; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: false,
      isPrevMonth: false,
      date: new Date(currentYear, currentMonth + 1, day)
    })
  }

  return (
    <Card className={cn('bg-white border-blue-200 shadow-lg', className)}>
      <CardHeader className="pb-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg text-gray-900 flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-blue-600" />
            Holiday Calendar
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('prev')}
              className="h-9 w-9 p-0 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="text-sm font-medium text-gray-700 min-w-[120px] text-center">
              {MONTHS[currentMonth]} {currentYear}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('next')}
              className="h-9 w-9 p-0 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {!readOnly && (
              <Dialog
                open={isAddDialogOpen || !!editingHoliday}
                onOpenChange={(open) => {
                  if (!open) {
                    setIsAddDialogOpen(false)
                    setEditingHoliday(null)
                  }
                }}
              >
                <DialogTrigger asChild>
                  <Button
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Holiday
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      {editingHoliday ? 'Edit Holiday' : 'Add New Holiday'}
                    </DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="holiday-name">Holiday Name</Label>
                      <Input
                        id="holiday-name"
                        value={newHoliday.name}
                        onChange={(e) => setNewHoliday(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter holiday name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="holiday-date">Date</Label>
                      <Input
                        id="holiday-date"
                        type="date"
                        value={newHoliday.date}
                        onChange={(e) => setNewHoliday(prev => ({ ...prev, date: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="holiday-type">Type</Label>
                      <select
                        id="holiday-type"
                        value={newHoliday.type}
                        onChange={(e) => setNewHoliday(prev => ({ ...prev, type: e.target.value as Holiday['type'] }))}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="national">National Holiday</option>
                        <option value="clinic">Clinic Holiday</option>
                        <option value="personal">Personal Day</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="holiday-description">Description (Optional)</Label>
                      <Input
                        id="holiday-description"
                        value={newHoliday.description}
                        onChange={(e) => setNewHoliday(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter description"
                      />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsAddDialogOpen(false)
                          setEditingHoliday(null)
                        }}
                      >
                        Cancel
                      </Button>
                      <Button onClick={editingHoliday ? handleUpdateHoliday : handleAddHoliday}>
                        {editingHoliday ? 'Update' : 'Add'} Holiday
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {/* Days of week header */}
        <div className="grid grid-cols-7 border-b border-gray-200 bg-gray-50">
          {DAYS_OF_WEEK.map((day) => (
            <div
              key={day}
              className="p-3 text-center text-sm font-medium text-gray-600 border-r border-gray-200 last:border-r-0"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7">
          {calendarDays.map((dateInfo, index) => {
            const { day, isCurrentMonth, date } = dateInfo
            const dayHolidays = getHolidaysForDate(date)
            const isToday = date.toDateString() === new Date().toDateString()

            return (
              <div
                key={index}
                className={cn(
                  'min-h-[80px] p-2 border-r border-b border-gray-200 last:border-r-0',
                  {
                    'bg-gray-50': !isCurrentMonth,
                    'bg-blue-50': isToday && isCurrentMonth,
                  }
                )}
              >
                <div className={cn(
                  'text-sm font-medium mb-1',
                  {
                    'text-gray-400': !isCurrentMonth,
                    'text-blue-600': isToday && isCurrentMonth,
                    'text-gray-900': isCurrentMonth && !isToday
                  }
                )}>
                  {day}
                </div>
                
                <div className="space-y-1">
                  {dayHolidays.map((holiday) => (
                    <div
                      key={holiday.id}
                      className={cn(
                        'text-xs px-2 py-1 rounded border truncate cursor-pointer hover:shadow-sm transition-shadow',
                        getHolidayTypeColor(holiday.type)
                      )}
                      title={`${holiday.name}${holiday.description ? ` - ${holiday.description}` : ''}`}
                      onClick={() => handleEditHoliday(holiday)}
                    >
                      {holiday.name}
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>

        {/* Legend */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded bg-red-100 border border-red-300"></div>
              <span className="text-gray-600">National Holiday</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded bg-blue-100 border border-blue-300"></div>
              <span className="text-gray-600">Clinic Holiday</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded bg-green-100 border border-green-300"></div>
              <span className="text-gray-600">Personal Day</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
