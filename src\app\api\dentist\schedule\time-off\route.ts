import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { timeOffCreateSchema } from '@/lib/validations'
import { UserRole, TimeOffStatus } from '@prisma/client'

// GET /api/dentist/schedule/time-off - Get time off requests for a dentist
export async function GET(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const { searchParams } = new URL(request.url)
      const dentistId = searchParams.get('dentistId')
      const status = searchParams.get('status')
      const startDate = searchParams.get('startDate')
      const endDate = searchParams.get('endDate')

      // Determine which dentist's time off to fetch
      let targetDentistId = dentistId
      if (!targetDentistId) {
        // If no dentistId provided, use current user's dentist profile
        if (user.role !== UserRole.DENTIST) {
          return NextResponse.json(
            { error: 'Dentist ID is required for non-dentist users' },
            { status: 400 }
          )
        }
        
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile) {
          return NextResponse.json(
            { error: 'Dentist profile not found' },
            { status: 404 }
          )
        }
        
        targetDentistId = dentistProfile.id
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== targetDentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to view this time off data' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Build query conditions
      const whereConditions: any = {
        dentistId: targetDentistId,
      }

      if (status) {
        whereConditions.status = status as TimeOffStatus
      }

      if (startDate && endDate) {
        whereConditions.OR = [
          {
            startDate: {
              gte: new Date(startDate),
              lte: new Date(endDate)
            }
          },
          {
            endDate: {
              gte: new Date(startDate),
              lte: new Date(endDate)
            }
          },
          {
            startDate: { lte: new Date(startDate) },
            endDate: { gte: new Date(endDate) }
          }
        ]
      }

      const timeOffRequests = await prisma.dentistTimeOff.findMany({
        where: whereConditions,
        orderBy: { startDate: 'asc' },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: timeOffRequests
      })

    } catch (error) {
      console.error('Error fetching time off requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch time off requests' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}

// POST /api/dentist/schedule/time-off - Create time off request
export async function POST(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const body = await request.json()
      const validatedData = timeOffCreateSchema.parse(body)

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== validatedData.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to create time off for this dentist' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Verify dentist exists
      const dentist = await prisma.dentistProfile.findUnique({
        where: { id: validatedData.dentistId }
      })

      if (!dentist) {
        return NextResponse.json(
          { error: 'Dentist not found' },
          { status: 404 }
        )
      }

      // Check for overlapping time off requests
      const overlappingRequests = await prisma.dentistTimeOff.findMany({
        where: {
          dentistId: validatedData.dentistId,
          status: { in: [TimeOffStatus.PENDING, TimeOffStatus.APPROVED] },
          OR: [
            {
              startDate: {
                lte: validatedData.endDate
              },
              endDate: {
                gte: validatedData.startDate
              }
            }
          ]
        }
      })

      if (overlappingRequests.length > 0) {
        return NextResponse.json(
          { error: 'Time off request overlaps with existing approved or pending requests' },
          { status: 409 }
        )
      }

      // Create time off request
      const timeOffRequest = await prisma.dentistTimeOff.create({
        data: {
          dentistId: validatedData.dentistId,
          title: validatedData.title,
          startDate: validatedData.startDate,
          endDate: validatedData.endDate,
          timeOffType: validatedData.timeOffType,
          reason: validatedData.reason,
          isAllDay: validatedData.isAllDay,
          notes: validatedData.notes,
          // Auto-approve if created by admin/staff, otherwise pending
          status: [UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string) 
            ? TimeOffStatus.APPROVED 
            : TimeOffStatus.PENDING,
          approvedBy: [UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string) 
            ? user.id 
            : undefined,
          approvedAt: [UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string) 
            ? new Date() 
            : undefined,
        },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: timeOffRequest
      }, { status: 201 })

    } catch (error) {
      console.error('Error creating time off request:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create time off request' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}
