import { z } from 'zod'
import { UserRole, AppointmentStatus, AppointmentType, PaymentMethod, TimeBlockType, TimeOffType, TimeOffStatus } from '@prisma/client'

// User validation schemas
export const userCreateSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().optional(),
  role: z.nativeEnum(UserRole),
})

export const userUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().optional(),
  image: z.string().url().optional(),
})

// Patient profile validation schemas
export const patientProfileCreateSchema = z.object({
  dateOfBirth: z.string().transform((str) => new Date(str)).optional(),
  gender: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyPhone: z.string().optional(),
  insuranceProvider: z.string().optional(),
  insuranceNumber: z.string().optional(),
  medicalHistory: z.string().optional(),
  allergies: z.string().optional(),
  medications: z.string().optional(),
})

export const patientProfileUpdateSchema = patientProfileCreateSchema.partial()

// Combined patient and user update schema for comprehensive patient editing
export const patientFullUpdateSchema = z.object({
  // User fields
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED']).optional(),

  // Patient profile fields
  dateOfBirth: z.string().transform((str) => new Date(str)).optional(),
  gender: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyPhone: z.string().optional(),
  insuranceProvider: z.string().optional(),
  insuranceNumber: z.string().optional(),
  medicalHistory: z.string().optional(),
  allergies: z.string().optional(),
  medications: z.string().optional(),
})

// Dentist profile validation schemas
export const dentistProfileCreateSchema = z.object({
  licenseNumber: z.string().min(1, 'License number is required'),
  specialization: z.string().optional(),
  yearsExperience: z.number().min(0).optional(),
  education: z.string().optional(),
  certifications: z.string().optional(),
  bio: z.string().optional(),
  consultationFee: z.number().min(0).optional(),
  isAvailable: z.boolean().default(true),
})

export const dentistProfileUpdateSchema = dentistProfileCreateSchema.partial()

// Staff profile validation schemas
export const staffProfileCreateSchema = z.object({
  position: z.string().min(1, 'Position is required'),
  department: z.string().optional(),
  hireDate: z.string().transform((str) => new Date(str)),
  salary: z.number().min(0).optional(),
  permissions: z.array(z.string()).default([]),
})

export const staffProfileUpdateSchema = staffProfileCreateSchema.partial()

// Appointment validation schemas
export const appointmentCreateSchema = z.object({
  patientId: z.string().cuid(),
  dentistId: z.string().cuid(),
  branchId: z.string().cuid(),
  serviceId: z.string().cuid(),
  scheduledAt: z.string().transform((str) => new Date(str)),
  duration: z.number().min(15).max(480), // 15 minutes to 8 hours
  type: z.nativeEnum(AppointmentType).default(AppointmentType.CONSULTATION),
  notes: z.string().optional(),
  symptoms: z.string().optional(),
})

export const appointmentUpdateSchema = z.object({
  scheduledAt: z.string().transform((str) => new Date(str)).optional(),
  duration: z.number().min(15).max(480).optional(),
  status: z.nativeEnum(AppointmentStatus).optional(),
  type: z.nativeEnum(AppointmentType).optional(),
  notes: z.string().optional(),
  symptoms: z.string().optional(),
  cancelReason: z.string().optional(),
})

// Treatment validation schemas
export const treatmentCreateSchema = z.object({
  appointmentId: z.string().cuid().optional(),
  patientId: z.string().cuid(),
  dentistId: z.string().cuid(),
  serviceId: z.string().cuid(),
  diagnosis: z.string().optional(),
  procedure: z.string().optional(),
  notes: z.string().optional(),
  cost: z.number().min(0),
})

export const treatmentUpdateSchema = treatmentCreateSchema.partial().extend({
  status: z.enum(['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
})

// Service validation schemas
export const serviceCreateSchema = z.object({
  name: z.string().min(1, 'Service name is required'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  duration: z.number().min(15), // minimum 15 minutes
  price: z.number().min(0),
  isActive: z.boolean().default(true),
})

export const serviceUpdateSchema = serviceCreateSchema.partial()

// Branch validation schemas
export const branchCreateSchema = z.object({
  name: z.string().min(1, 'Branch name is required'),
  address: z.string().min(1, 'Address is required'),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
})

export const branchUpdateSchema = branchCreateSchema.partial()

// Invoice validation schemas
export const invoiceCreateSchema = z.object({
  patientId: z.string().cuid(),
  items: z.array(z.object({
    treatmentId: z.string().cuid().optional(),
    description: z.string().min(1),
    quantity: z.number().min(1).default(1),
    unitPrice: z.number().min(0),
  })),
  dueDate: z.string().transform((str) => new Date(str)),
  notes: z.string().optional(),
})

// Payment validation schemas
export const paymentCreateSchema = z.object({
  invoiceId: z.string().cuid(),
  amount: z.number().min(0),
  method: z.nativeEnum(PaymentMethod),
  transactionId: z.string().optional(),
  notes: z.string().optional(),
})

// Inventory validation schemas
export const inventoryItemCreateSchema = z.object({
  branchId: z.string().cuid(),
  name: z.string().min(1, 'Item name is required'),
  sku: z.string().min(1, 'SKU is required'),
  category: z.string().min(1, 'Category is required'),
  description: z.string().optional(),
  supplier: z.string().optional(),
  unitPrice: z.number().min(0).optional(),
  currentStock: z.number().min(0).default(0),
  minStock: z.number().min(0).default(0),
  maxStock: z.number().min(0).optional(),
  unit: z.string().min(1, 'Unit is required'),
  expiryDate: z.string().transform((str) => new Date(str)).optional(),
  isActive: z.boolean().default(true),
})

export const inventoryItemUpdateSchema = inventoryItemCreateSchema.partial()

// Common validation helpers
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const searchSchema = z.object({
  query: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

export type UserCreateInput = z.infer<typeof userCreateSchema>
export type UserUpdateInput = z.infer<typeof userUpdateSchema>
export type PatientProfileCreateInput = z.infer<typeof patientProfileCreateSchema>
export type PatientProfileUpdateInput = z.infer<typeof patientProfileUpdateSchema>
export type DentistProfileCreateInput = z.infer<typeof dentistProfileCreateSchema>
export type DentistProfileUpdateInput = z.infer<typeof dentistProfileUpdateSchema>
export type StaffProfileCreateInput = z.infer<typeof staffProfileCreateSchema>
export type StaffProfileUpdateInput = z.infer<typeof staffProfileUpdateSchema>
export type AppointmentCreateInput = z.infer<typeof appointmentCreateSchema>
export type AppointmentUpdateInput = z.infer<typeof appointmentUpdateSchema>
export type TreatmentCreateInput = z.infer<typeof treatmentCreateSchema>
export type TreatmentUpdateInput = z.infer<typeof treatmentUpdateSchema>
export type ServiceCreateInput = z.infer<typeof serviceCreateSchema>
export type ServiceUpdateInput = z.infer<typeof serviceUpdateSchema>
export type BranchCreateInput = z.infer<typeof branchCreateSchema>
export type BranchUpdateInput = z.infer<typeof branchUpdateSchema>
export type InvoiceCreateInput = z.infer<typeof invoiceCreateSchema>
export type PaymentCreateInput = z.infer<typeof paymentCreateSchema>
export type InventoryItemCreateInput = z.infer<typeof inventoryItemCreateSchema>
export type InventoryItemUpdateInput = z.infer<typeof inventoryItemUpdateSchema>
export type PaginationInput = z.infer<typeof paginationSchema>
export type SearchInput = z.infer<typeof searchSchema>

// ============================================================================
// SCHEDULE MANAGEMENT VALIDATION SCHEMAS
// ============================================================================

// Time validation helper
const timeStringSchema = z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)')

// Working hours validation schemas
export const workingHoursCreateSchema = z.object({
  dentistId: z.string().cuid('Invalid dentist ID'),
  dayOfWeek: z.number().int().min(0).max(6), // 0 = Sunday, 6 = Saturday
  startTime: timeStringSchema,
  endTime: timeStringSchema,
  lunchStart: timeStringSchema.optional(),
  lunchEnd: timeStringSchema.optional(),
  breakSlots: z.array(z.object({
    startTime: timeStringSchema,
    endTime: timeStringSchema,
    title: z.string().optional()
  })).optional(),
  effectiveFrom: z.string().transform((str) => new Date(str)).optional(),
  effectiveTo: z.string().transform((str) => new Date(str)).optional(),
}).refine((data) => {
  // Validate that end time is after start time
  const [startHour, startMin] = data.startTime.split(':').map(Number)
  const [endHour, endMin] = data.endTime.split(':').map(Number)
  const startMinutes = startHour * 60 + startMin
  const endMinutes = endHour * 60 + endMin
  return endMinutes > startMinutes
}, {
  message: 'End time must be after start time',
  path: ['endTime']
}).refine((data) => {
  // Validate lunch break times if provided
  if (data.lunchStart && data.lunchEnd) {
    const [lunchStartHour, lunchStartMin] = data.lunchStart.split(':').map(Number)
    const [lunchEndHour, lunchEndMin] = data.lunchEnd.split(':').map(Number)
    const lunchStartMinutes = lunchStartHour * 60 + lunchStartMin
    const lunchEndMinutes = lunchEndHour * 60 + lunchEndMin
    return lunchEndMinutes > lunchStartMinutes
  }
  return true
}, {
  message: 'Lunch end time must be after lunch start time',
  path: ['lunchEnd']
})

export const workingHoursUpdateSchema = z.object({
  dayOfWeek: z.number().min(0).max(6).optional(),
  startTime: timeStringSchema.optional(),
  endTime: timeStringSchema.optional(),
  lunchStart: timeStringSchema.optional(),
  lunchEnd: timeStringSchema.optional(),
  breakSlots: z.array(z.object({
    startTime: timeStringSchema,
    endTime: timeStringSchema,
    title: z.string().optional()
  })).optional(),
  effectiveFrom: z.string().transform(str => new Date(str)).optional(),
  effectiveTo: z.string().transform(str => new Date(str)).optional(),
}).refine(data => {
  if (data.startTime && data.endTime) {
    return data.startTime < data.endTime
  }
  return true
}, { message: "End time must be after start time" })
.refine(data => {
  if (data.lunchStart && data.lunchEnd) {
    return data.lunchStart < data.lunchEnd
  }
  return true
}, { message: "Lunch end time must be after lunch start time" })

// Time block validation schemas
export const timeBlockCreateSchema = z.object({
  dentistId: z.string().cuid('Invalid dentist ID'),
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  description: z.string().max(500, 'Description too long').optional(),
  startTime: z.string().transform((str) => new Date(str)),
  endTime: z.string().transform((str) => new Date(str)),
  blockType: z.nativeEnum(TimeBlockType),
  isRecurring: z.boolean().default(false),
  recurringPattern: z.string().optional(), // JSON string for recurring rules
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color').optional(),
}).refine((data) => {
  // Validate that end time is after start time
  return data.endTime > data.startTime
}, {
  message: 'End time must be after start time',
  path: ['endTime']
})

export const timeBlockUpdateSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  startTime: z.string().transform(str => new Date(str)).optional(),
  endTime: z.string().transform(str => new Date(str)).optional(),
  blockType: z.nativeEnum(TimeBlockType).optional(),
  isRecurring: z.boolean().default(false).optional(),
  recurringPattern: z.record(z.any()).optional(),
  color: z.string().optional(),
}).refine(data => {
  if (data.startTime && data.endTime) {
    return data.startTime < data.endTime
  }
  return true
}, { message: "End time must be after start time" })

// Time off validation schemas
export const timeOffCreateSchema = z.object({
  dentistId: z.string().cuid('Invalid dentist ID'),
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)),
  timeOffType: z.nativeEnum(TimeOffType),
  reason: z.string().max(500, 'Reason too long').optional(),
  isAllDay: z.boolean().default(true),
  notes: z.string().max(1000, 'Notes too long').optional(),
}).refine((data) => {
  // Validate that end date is after or equal to start date
  return data.endDate >= data.startDate
}, {
  message: 'End date must be after or equal to start date',
  path: ['endDate']
})

export const timeOffUpdateSchema = z.object({
  title: z.string().min(1).optional(),
  startDate: z.string().transform(str => new Date(str)).optional(),
  endDate: z.string().transform(str => new Date(str)).optional(),
  timeOffType: z.nativeEnum(TimeOffType).optional(),
  reason: z.string().optional(),
  isAllDay: z.boolean().default(true).optional(),
  notes: z.string().optional(),
  status: z.nativeEnum(TimeOffStatus).optional(),
  approvedBy: z.string().cuid().optional(),
}).refine(data => {
  if (data.startDate && data.endDate) {
    return data.startDate <= data.endDate
  }
  return true
}, { message: "End date must be after or equal to start date" })

// Schedule template validation schemas
export const scheduleTemplateCreateSchema = z.object({
  dentistId: z.string().cuid('Invalid dentist ID'),
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  templateData: z.object({
    workingHours: z.array(z.object({
      dayOfWeek: z.number().int().min(0).max(6),
      startTime: timeStringSchema,
      endTime: timeStringSchema,
      lunchStart: timeStringSchema.optional(),
      lunchEnd: timeStringSchema.optional(),
      breakSlots: z.array(z.object({
        startTime: timeStringSchema,
        endTime: timeStringSchema,
        title: z.string().optional()
      })).optional(),
    })),
    timeBlocks: z.array(z.object({
      title: z.string(),
      description: z.string().optional(),
      dayOfWeek: z.number().int().min(0).max(6),
      startTime: timeStringSchema,
      endTime: timeStringSchema,
      blockType: z.nativeEnum(TimeBlockType),
      color: z.string().optional(),
    })).optional(),
  }),
  isDefault: z.boolean().default(false),
})

export const scheduleTemplateUpdateSchema = scheduleTemplateCreateSchema.partial().omit({ dentistId: true })

// Bulk schedule operations
export const bulkScheduleUpdateSchema = z.object({
  dentistId: z.string().cuid('Invalid dentist ID'),
  operation: z.enum(['apply_template', 'copy_week', 'clear_schedule']),
  templateId: z.string().cuid().optional(),
  sourceWeek: z.string().transform((str) => new Date(str)).optional(),
  targetWeek: z.string().transform((str) => new Date(str)).optional(),
  dateRange: z.object({
    startDate: z.string().transform((str) => new Date(str)),
    endDate: z.string().transform((str) => new Date(str)),
  }).optional(),
})

// Export type definitions
export type WorkingHoursCreateInput = z.infer<typeof workingHoursCreateSchema>
export type WorkingHoursUpdateInput = z.infer<typeof workingHoursUpdateSchema>
export type TimeBlockCreateInput = z.infer<typeof timeBlockCreateSchema>
export type TimeBlockUpdateInput = z.infer<typeof timeBlockUpdateSchema>
export type TimeOffCreateInput = z.infer<typeof timeOffCreateSchema>
export type TimeOffUpdateInput = z.infer<typeof timeOffUpdateSchema>
export type ScheduleTemplateCreateInput = z.infer<typeof scheduleTemplateCreateSchema>
export type ScheduleTemplateUpdateInput = z.infer<typeof scheduleTemplateUpdateSchema>
export type BulkScheduleUpdateInput = z.infer<typeof bulkScheduleUpdateSchema>
