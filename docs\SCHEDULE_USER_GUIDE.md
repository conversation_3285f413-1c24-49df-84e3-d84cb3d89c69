# 📅 Dentist Schedule Management - User Guide

## Overview
The Schedule Management system allows dentists to configure their working hours, manage time blocks, set recurring patterns, handle holidays, and customize schedule preferences.

## Getting Started

### Accessing Schedule Management
1. Log in to your dentist account
2. Navigate to **Dentist Dashboard**
3. Click on **Schedule** in the sidebar
4. You'll see 5 tabs: Weekly View, Time Blocks, Patterns, Holidays, Settings

## Tab-by-Tab User Guide

### 1. 📊 Weekly View Tab

**Purpose:** View and manage your weekly schedule in a calendar grid format.

**Features:**
- Interactive weekly calendar showing all time slots
- Color-coded time blocks:
  - 🔵 **Blue**: Working hours
  - 🟢 **Green**: Break times
  - 🔴 **Red**: Blocked/unavailable time
  - 🟡 **Yellow**: Appointments

**How to Use:**
1. **View Schedule**: See your current week's schedule at a glance
2. **Navigate Weeks**: Use arrow buttons to move between weeks
3. **Add Time Slots**: Click on empty time slots to add working hours
4. **Edit Existing Slots**: Click on existing time blocks to modify them
5. **Delete Slots**: Use the delete button on time blocks to remove them

**Best Practices:**
- Set consistent working hours for better patient booking experience
- Include adequate break times between appointments
- Block time for administrative tasks and emergencies

### 2. ⏰ Time Blocks Tab

**Purpose:** Create and manage specific time blocks for procedures, breaks, and personal time.

**Time Block Types:**
- **Procedure**: Extended time for complex treatments
- **Break**: Rest periods and meal breaks
- **Personal**: Personal appointments or commitments
- **Emergency**: Buffer time for urgent cases
- **Admin**: Administrative tasks and paperwork

**How to Use:**
1. **Add Time Block**: Click "Add Time Block" button
2. **Select Type**: Choose the appropriate block type
3. **Set Duration**: Define start and end times
4. **Add Description**: Provide details about the time block
5. **Save Changes**: Confirm your time block creation

**Workflow Example:**
```
1. Click "Add Time Block"
2. Select "Procedure" type
3. Set time: 2:00 PM - 4:00 PM
4. Title: "Root Canal Treatment"
5. Description: "Complex procedure requiring extended time"
6. Click "Save"
```

### 3. 🔄 Patterns Tab

**Purpose:** Set up recurring schedule patterns for consistent weekly routines.

**Pattern Types:**
- **Daily**: Same schedule every day
- **Weekly**: Different schedule for each day of the week
- **Monthly**: Patterns that repeat monthly
- **Custom**: Flexible patterns for unique schedules

**How to Use:**
1. **Choose Pattern Type**: Select daily, weekly, or monthly
2. **Set Interval**: Define how often the pattern repeats
3. **Select Days**: Choose which days the pattern applies to
4. **Configure Times**: Set working hours for the pattern
5. **Set End Date**: Define when the pattern should stop (optional)

**Common Patterns:**
- **Monday-Friday 9-5**: Standard business hours
- **Tuesday/Thursday Extended**: Longer hours on specific days
- **Weekend Half-Days**: Saturday morning clinics

### 4. 🏖️ Holidays Tab

**Purpose:** Manage holidays, vacation days, and clinic closures.

**Holiday Types:**
- **National**: Government holidays (auto-populated)
- **Clinic**: Clinic-specific closures
- **Personal**: Your personal vacation days
- **Emergency**: Unexpected closures

**How to Use:**
1. **View Calendar**: See all holidays marked on the calendar
2. **Add Holiday**: Click on a date to add a new holiday
3. **Edit Holiday**: Click on existing holidays to modify details
4. **Delete Holiday**: Remove holidays that are no longer applicable
5. **Import Holidays**: Bulk import national holidays

**Holiday Management Workflow:**
```
1. Navigate to desired month/year
2. Click on the date for your vacation
3. Select "Personal" holiday type
4. Enter title: "Family Vacation"
5. Add description: "Out of town - emergency contact: Dr. Smith"
6. Save holiday
```

### 5. ⚙️ Settings Tab

**Purpose:** Configure default preferences and schedule management settings.

**Settings Categories:**

#### Default Working Hours
- Set your standard working day template
- Define typical start and end times
- Configure default break periods
- Save as template for quick setup

#### Schedule Preferences
- **Appointment Buffer Time**: Time between appointments
- **Break Duration Settings**: Standard break lengths
- **Booking Window Limits**: How far in advance patients can book
- **Emergency Slot Configuration**: Reserved slots for urgent cases

**Configuration Steps:**
1. **Set Default Hours**: Configure your typical 9 AM - 5 PM schedule
2. **Buffer Time**: Set 15-minute buffers between appointments
3. **Break Settings**: Configure 1-hour lunch breaks
4. **Booking Limits**: Allow booking up to 30 days in advance
5. **Emergency Slots**: Reserve 2 slots daily for urgent cases

## Common Workflows

### Setting Up Your First Schedule

1. **Start with Settings Tab**
   - Configure default working hours (e.g., 9 AM - 5 PM)
   - Set appointment buffer time (15 minutes)
   - Configure break preferences

2. **Create Weekly Pattern**
   - Go to Patterns tab
   - Select "Weekly" pattern
   - Set Monday-Friday working hours
   - Configure Saturday half-days if needed

3. **Add Time Blocks**
   - Go to Time Blocks tab
   - Add lunch breaks (12 PM - 1 PM daily)
   - Block administrative time (8:30 AM - 9 AM)
   - Reserve emergency slots

4. **Mark Holidays**
   - Go to Holidays tab
   - Import national holidays
   - Add your planned vacation days
   - Mark clinic closure days

### Modifying Your Schedule

1. **Temporary Changes** (Use Weekly View)
   - Block specific time slots for one-time events
   - Extend hours for special appointments
   - Add emergency time blocks

2. **Permanent Changes** (Use Patterns)
   - Modify recurring weekly schedule
   - Change standard working hours
   - Update break times

### Managing Vacation Time

1. **Plan Ahead**
   - Add vacation dates to Holidays tab
   - Set "Personal" holiday type
   - Include emergency contact information

2. **Notify System**
   - Vacation automatically blocks appointment booking
   - Existing appointments show conflict warnings
   - Patients receive automatic notifications

## Integration with Appointment Booking

### How Schedule Affects Booking
- **Available Slots**: Only open time slots appear for patient booking
- **Buffer Time**: Automatic spacing between appointments
- **Holiday Blocking**: No appointments can be booked on holidays
- **Time Block Respect**: Blocked time is unavailable for booking

### Real-time Updates
- Schedule changes immediately affect availability
- Patients see updated time slots instantly
- Conflicts are automatically detected and prevented

## Validation Rules

### Time Constraints
- Working hours must be within 6 AM - 10 PM
- Minimum appointment slot: 15 minutes
- Maximum daily working hours: 12 hours
- Breaks must be at least 15 minutes

### Conflict Prevention
- Overlapping time blocks are not allowed
- Appointments cannot be scheduled during breaks
- Holiday dates block all appointment booking
- Emergency slots cannot be double-booked

## Troubleshooting

### Common Issues

**"Schedule Conflict Detected"**
- Check for overlapping time blocks
- Verify working hours don't exceed limits
- Ensure breaks don't conflict with appointments

**"Cannot Save Changes"**
- Verify all required fields are filled
- Check that times are in correct format
- Ensure end time is after start time

**"Appointments Not Showing"**
- Confirm working hours are set
- Check that date is not marked as holiday
- Verify time blocks aren't blocking availability

### Getting Help
- Contact system administrator for technical issues
- Refer to this guide for workflow questions
- Check the FAQ section for common problems

## Best Practices

### Schedule Optimization
- Maintain consistent daily routines
- Allow adequate time between complex procedures
- Schedule administrative tasks during low-demand periods
- Keep emergency slots available daily

### Patient Experience
- Provide clear availability windows
- Maintain reasonable booking lead times
- Communicate schedule changes promptly
- Offer alternative times when possible

### Work-Life Balance
- Set realistic working hours
- Schedule regular breaks
- Plan vacation time in advance
- Use time blocking for non-clinical tasks
