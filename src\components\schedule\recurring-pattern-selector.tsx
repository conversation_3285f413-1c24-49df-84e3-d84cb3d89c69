'use client'

import * as React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { cn } from '@/lib/utils'
import { Repeat, Calendar } from 'lucide-react'

interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'custom'
  interval: number // Every X days/weeks/months
  daysOfWeek?: number[] // 0-6, Sunday = 0
  dayOfMonth?: number // 1-31
  endDate?: Date
  occurrences?: number
  startTime?: string // Working hours start time
  endTime?: string // Working hours end time
}

interface RecurringPatternSelectorProps {
  value?: RecurringPattern
  onChange?: (pattern: RecurringPattern) => void
  className?: string
  title?: string
  description?: string
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday', short: 'Sun' },
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' }
]

export function RecurringPatternSelector({
  value,
  onChange,
  className,
  title = "Recurring Pattern",
  description = "Set up recurring schedule pattern"
}: RecurringPatternSelectorProps) {
  const [pattern, setPattern] = React.useState<RecurringPattern>(
    value || {
      type: 'weekly',
      interval: 1,
      daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
      startTime: '09:00',
      endTime: '17:00'
    }
  )

  // Update parent when pattern changes
  React.useEffect(() => {
    onChange?.(pattern)
  }, [pattern, onChange])

  const updatePattern = (updates: Partial<RecurringPattern>) => {
    setPattern(prev => ({ ...prev, ...updates }))
  }

  const toggleDayOfWeek = (dayValue: number) => {
    const currentDays = pattern.daysOfWeek || []
    const newDays = currentDays.includes(dayValue)
      ? currentDays.filter(d => d !== dayValue)
      : [...currentDays, dayValue].sort()
    
    updatePattern({ daysOfWeek: newDays })
  }

  const formatPatternSummary = () => {
    const { type, interval, daysOfWeek, dayOfMonth } = pattern
    
    switch (type) {
      case 'daily':
        return interval === 1 ? 'Every day' : `Every ${interval} days`
      
      case 'weekly':
        const dayNames = daysOfWeek?.map(d => DAYS_OF_WEEK[d].short).join(', ') || ''
        const weekText = interval === 1 ? 'Every week' : `Every ${interval} weeks`
        return `${weekText} on ${dayNames}`
      
      case 'monthly':
        const monthText = interval === 1 ? 'Every month' : `Every ${interval} months`
        return `${monthText} on day ${dayOfMonth || 1}`
      
      case 'custom':
        return 'Custom pattern'
      
      default:
        return 'No pattern selected'
    }
  }

  return (
    <Card className={cn('bg-white border-blue-200 shadow-lg', className)}>
      <CardHeader className="pb-4 border-b border-gray-100">
        <CardTitle className="text-lg text-gray-900 flex items-center">
          <Repeat className="h-5 w-5 mr-2 text-blue-600" />
          {title}
        </CardTitle>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </CardHeader>

      <CardContent className="pt-6 space-y-6">
        {/* Pattern Type Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-700">Repeat Pattern</Label>
          <Select 
            value={pattern.type} 
            onValueChange={(value: RecurringPattern['type']) => updatePattern({ type: value })}
          >
            <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Interval Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-700">
            Repeat Every
          </Label>
          <div className="flex items-center space-x-3">
            <Input
              type="number"
              min="1"
              max="52"
              value={pattern.interval}
              onChange={(e) => updatePattern({ interval: parseInt(e.target.value) || 1 })}
              className="w-20 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-600">
              {pattern.type === 'daily' && (pattern.interval === 1 ? 'day' : 'days')}
              {pattern.type === 'weekly' && (pattern.interval === 1 ? 'week' : 'weeks')}
              {pattern.type === 'monthly' && (pattern.interval === 1 ? 'month' : 'months')}
              {pattern.type === 'custom' && 'intervals'}
            </span>
          </div>
        </div>

        {/* Working Hours Time Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-700">Working Hours</Label>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">Start Time</Label>
              <Input
                type="time"
                value={pattern.startTime || '09:00'}
                onChange={(e) => updatePattern({ startTime: e.target.value })}
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">End Time</Label>
              <Input
                type="time"
                value={pattern.endTime || '17:00'}
                onChange={(e) => updatePattern({ endTime: e.target.value })}
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Weekly Pattern - Days of Week */}
        {pattern.type === 'weekly' && (
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">Days of Week</Label>
            <div className="grid grid-cols-7 gap-2">
              {DAYS_OF_WEEK.map((day) => (
                <div key={day.value} className="flex flex-col items-center">
                  <Checkbox
                    id={`day-${day.value}`}
                    checked={pattern.daysOfWeek?.includes(day.value) || false}
                    onCheckedChange={() => toggleDayOfWeek(day.value)}
                    className="mb-2"
                  />
                  <Label 
                    htmlFor={`day-${day.value}`}
                    className="text-xs text-gray-600 cursor-pointer"
                  >
                    {day.short}
                  </Label>
                </div>
              ))}
            </div>
            
            {/* Quick Day Selection Presets */}
            <div className="flex flex-wrap gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => updatePattern({ daysOfWeek: [1, 2, 3, 4, 5] })}
                className="text-xs border-gray-300 hover:bg-blue-50"
              >
                Weekdays
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => updatePattern({ daysOfWeek: [0, 6] })}
                className="text-xs border-gray-300 hover:bg-blue-50"
              >
                Weekends
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => updatePattern({ daysOfWeek: [0, 1, 2, 3, 4, 5, 6] })}
                className="text-xs border-gray-300 hover:bg-blue-50"
              >
                All Days
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => updatePattern({ daysOfWeek: [] })}
                className="text-xs border-gray-300 hover:bg-blue-50"
              >
                Clear
              </Button>
            </div>
          </div>
        )}

        {/* Monthly Pattern - Day of Month */}
        {pattern.type === 'monthly' && (
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">Day of Month</Label>
            <Select 
              value={pattern.dayOfMonth?.toString() || '1'} 
              onValueChange={(value) => updatePattern({ dayOfMonth: parseInt(value) })}
            >
              <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                  <SelectItem key={day} value={day.toString()}>
                    {day}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* End Condition */}
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-700">End Condition</Label>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Input
                type="date"
                value={pattern.endDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => updatePattern({ 
                  endDate: e.target.value ? new Date(e.target.value) : undefined 
                })}
                className="flex-1 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => updatePattern({ endDate: undefined })}
                className="border-gray-300 hover:bg-red-50"
              >
                Clear
              </Button>
            </div>
            
            <div className="text-xs text-gray-500">
              Leave empty for indefinite recurrence
            </div>
          </div>
        </div>

        {/* Pattern Summary */}
        <div className="pt-4 border-t border-gray-100">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-start space-x-3">
              <Calendar className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-1">
                  Pattern Summary
                </h4>
                <p className="text-sm text-blue-700">
                  {formatPatternSummary()}
                </p>
                {pattern.endDate && (
                  <p className="text-xs text-blue-600 mt-1">
                    Until {pattern.endDate.toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric',
                      timeZone: 'Asia/Manila'
                    })}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
