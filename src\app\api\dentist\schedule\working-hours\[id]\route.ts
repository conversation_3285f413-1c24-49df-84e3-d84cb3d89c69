import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { workingHoursUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/dentist/schedule/working-hours/[id] - Get specific working hours
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
    try {
      const workingHours = await prisma.dentistWorkingHours.findUnique({
        where: { id: id },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      if (!workingHours) {
        return NextResponse.json(
          { error: 'Working hours not found' },
          { status: 404 }
        )
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== workingHours.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to view this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      return NextResponse.json({
        success: true,
        data: workingHours
      })

    } catch (error) {
      console.error('Error fetching working hours:', error)
      return NextResponse.json(
        { error: 'Failed to fetch working hours' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])

// PUT /api/dentist/schedule/working-hours/[id] - Update working hours
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
    try {
      const body = await req.json()
      const validatedData = workingHoursUpdateSchema.parse(body)

      // Check if working hours exist
      const existingWorkingHours = await prisma.dentistWorkingHours.findUnique({
        where: { id: id }
      })

      if (!existingWorkingHours) {
        return NextResponse.json(
          { error: 'Working hours not found' },
          { status: 404 }
        )
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== existingWorkingHours.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to modify this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Update working hours
      const updatedWorkingHours = await prisma.dentistWorkingHours.update({
        where: { id: id },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: updatedWorkingHours
      })

    } catch (error) {
      console.error('Error updating working hours:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to update working hours' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])

// DELETE /api/dentist/schedule/working-hours/[id] - Delete working hours
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
    try {
      // Check if working hours exist
      const existingWorkingHours = await prisma.dentistWorkingHours.findUnique({
        where: { id: id }
      })

      if (!existingWorkingHours) {
        return NextResponse.json(
          { error: 'Working hours not found' },
          { status: 404 }
        )
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== existingWorkingHours.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to modify this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Soft delete by setting isActive to false
      await prisma.dentistWorkingHours.update({
        where: { id: id },
        data: {
          isActive: false,
          updatedAt: new Date(),
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Working hours deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting working hours:', error)
      return NextResponse.json(
        { error: 'Failed to delete working hours' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])
