'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import {
  Loader2,
  Calendar,
  Clock,
  Plus,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Filter,
  List,
  Edit,
  Settings,
  Timer,
  Target,
  Download,
  Trash2
} from 'lucide-react'
import { UserRole, AppointmentStatus } from '@prisma/client'
import { DentistAppointmentList } from '@/components/dentist/appointment-list'
import { useAppointments } from '@/hooks/useAppointments'
import { toast } from 'sonner'

// Enhanced appointments state interface
interface AppointmentsState {
  activeView: 'overview' | 'calendar' | 'analytics'
  isRefreshing: boolean
  lastRefresh: Date
  hasError: boolean
  errorMessage?: string
  selectedAppointments: string[]
  bulkActionMode: boolean
}

// Confirmation dialog state
interface ConfirmDialogState {
  open: boolean
  title: string
  description: string
  onConfirm: () => Promise<void>
  loading: boolean
  variant?: 'default' | 'destructive'
}

export default function DentistAppointments() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [dentistProfileId, setDentistProfileId] = useState<string | null>(null)

  // Enhanced state management
  const [appointmentsState, setAppointmentsState] = useState<AppointmentsState>({
    activeView: 'overview',
    isRefreshing: false,
    lastRefresh: new Date(),
    hasError: false,
    selectedAppointments: [],
    bulkActionMode: false
  })

  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    open: false,
    title: '',
    description: '',
    onConfirm: async () => {},
    loading: false
  })

  // Get dentist profile ID
  useEffect(() => {
    const fetchDentistProfile = async () => {
      if (user?.id) {
        try {
          const response = await fetch(`/api/dentists/profile?userId=${user.id}`)
          if (response.ok) {
            const profile = await response.json()
            setDentistProfileId(profile.id)
          } else {
            console.error('Failed to fetch dentist profile:', response.status, response.statusText)
            // Handle specific error cases
            if (response.status === 404) {
              console.error('Dentist profile not found for user:', user.id)
              // You might want to redirect to profile setup or show an error message
            }
            // Set a placeholder or error state to prevent infinite loading
            setDentistProfileId('error')
          }
        } catch (error) {
          console.error('Failed to fetch dentist profile:', error)
          // Set error state to prevent infinite loading
          setDentistProfileId('error')
        }
      }
    }

    if (isAuthenticated && user?.role === UserRole.DENTIST) {
      fetchDentistProfile()
    }
  }, [user, isAuthenticated])

  // Get appointment statistics
  const {
    appointments,
    loading: statsLoading,
    getTodaysAppointments,
    getUpcomingAppointments,
    getAppointmentsByStatus
  } = useAppointments({
    dentistId: dentistProfileId || undefined,
    autoRefresh: true,
    refreshInterval: 30000
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/appointments')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  // Enhanced refresh functionality
  const refreshAppointments = useCallback(async () => {
    setAppointmentsState(prev => ({ ...prev, isRefreshing: true, hasError: false }))
    try {
      // Simulate API refresh
      await new Promise(resolve => setTimeout(resolve, 1000))
      setAppointmentsState(prev => ({
        ...prev,
        isRefreshing: false,
        lastRefresh: new Date()
      }))
      toast.success('Appointments refreshed successfully')
    } catch (_error) {
      setAppointmentsState(prev => ({
        ...prev,
        isRefreshing: false,
        hasError: true,
        errorMessage: 'Failed to refresh appointments data'
      }))
      toast.error('Failed to refresh appointments')
    }
  }, [])

  // Enhanced navigation handlers
  const handleViewDetails = (appointmentId: string) => {
    router.push(`/dentist/appointments/${appointmentId}`)
  }

  const handleNewAppointment = () => {
    router.push('/appointments/book')
  }

  const handleStatusUpdate = (appointmentId: string, status: AppointmentStatus, reason?: string) => {
    // Handle status update notifications or additional logic here
    console.log(`Appointment ${appointmentId} updated to ${status}`, reason)
    toast.success(`Appointment status updated to ${status}`)
  }

  // Bulk operations handlers
  const handleBulkAction = useCallback((action: 'confirm' | 'cancel' | 'reschedule' | 'delete') => {
    if (appointmentsState.selectedAppointments.length === 0) {
      toast.error('Please select appointments first')
      return
    }

    const actionMessages = {
      confirm: {
        title: 'Confirm Selected Appointments',
        description: `Are you sure you want to confirm ${appointmentsState.selectedAppointments.length} appointment(s)? This will send confirmation notifications to patients.`,
        variant: 'default' as const
      },
      cancel: {
        title: 'Cancel Selected Appointments',
        description: `Are you sure you want to cancel ${appointmentsState.selectedAppointments.length} appointment(s)? This action will notify patients and free up the time slots.`,
        variant: 'destructive' as const
      },
      reschedule: {
        title: 'Reschedule Selected Appointments',
        description: `You are about to reschedule ${appointmentsState.selectedAppointments.length} appointment(s). This will open the rescheduling interface.`,
        variant: 'default' as const
      },
      delete: {
        title: 'Delete Selected Appointments',
        description: `Are you sure you want to permanently delete ${appointmentsState.selectedAppointments.length} appointment(s)? This action cannot be undone.`,
        variant: 'destructive' as const
      }
    }

    const config = actionMessages[action]
    setConfirmDialog({
      open: true,
      title: config.title,
      description: config.description,
      variant: config.variant,
      loading: false,
      onConfirm: async () => {
        // Implement bulk action logic
        await new Promise(resolve => setTimeout(resolve, 1500))
        toast.success(`${action} completed for ${appointmentsState.selectedAppointments.length} appointments`)
        setAppointmentsState(prev => ({
          ...prev,
          selectedAppointments: [],
          bulkActionMode: false
        }))
      }
    })
  }, [appointmentsState.selectedAppointments])

  // Toggle bulk action mode
  const toggleBulkMode = useCallback(() => {
    setAppointmentsState(prev => ({
      ...prev,
      bulkActionMode: !prev.bulkActionMode,
      selectedAppointments: []
    }))
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  if (!dentistProfileId) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Setting up your profile...
            </h3>
            <p className="text-gray-600">
              Please wait while we load your dentist profile.
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (dentistProfileId === 'error') {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Profile Setup Required
            </h3>
            <p className="text-gray-600 mb-4">
              We couldn&apos;t find your dentist profile. This usually happens when:
            </p>
            <ul className="text-sm text-gray-500 mb-6 text-left space-y-1">
              <li>• Your account hasn&apos;t been set up as a dentist yet</li>
              <li>• Your profile is still being processed by the administrator</li>
              <li>• There was a temporary database issue</li>
            </ul>
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => router.push('/dashboard')}
                className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Go to Dashboard
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-4">
              If this problem persists, please contact your system administrator.
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Calculate statistics
  const todaysAppointments = getTodaysAppointments()
  const upcomingAppointments = getUpcomingAppointments()
  const completedAppointments = getAppointmentsByStatus('COMPLETED')
  const confirmedAppointments = getAppointmentsByStatus('CONFIRMED')

  // Calculate this week's appointments
  const startOfWeek = new Date()
  startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay())
  startOfWeek.setHours(0, 0, 0, 0)

  const endOfWeek = new Date(startOfWeek)
  endOfWeek.setDate(endOfWeek.getDate() + 6)
  endOfWeek.setHours(23, 59, 59, 999)

  const thisWeekAppointments = appointments.filter(appointment => {
    const appointmentDate = new Date(appointment.scheduledAt)
    return appointmentDate >= startOfWeek && appointmentDate <= endOfWeek
  })

  return (
    <div className="p-6 space-y-8">
      {/* Enhanced Header with Actions */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-transparent to-green-600/5 rounded-2xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-blue-100/50">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Appointments</h1>
                  <p className="text-gray-600 font-medium">
                    Manage your patient appointments and schedule
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Timer className="h-3 w-3" />
                  <span>Last updated: {appointmentsState.lastRefresh.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'Asia/Manila'
                  })}</span>
                </div>
                {appointmentsState.bulkActionMode && (
                  <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                    <Edit className="h-3 w-3 mr-1" />
                    Bulk Mode ({appointmentsState.selectedAppointments.length} selected)
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshAppointments}
                disabled={appointmentsState.isRefreshing}
                className="hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
              >
                {appointmentsState.isRefreshing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleBulkMode}
                className={`transition-all duration-200 ${
                  appointmentsState.bulkActionMode
                    ? 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100'
                    : 'hover:bg-purple-50 hover:border-purple-200'
                }`}
              >
                <Edit className="h-4 w-4 mr-2" />
                {appointmentsState.bulkActionMode ? 'Exit Bulk Mode' : 'Bulk Actions'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/schedule')}
                className="hover:bg-green-50 hover:border-green-200 transition-all duration-200"
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage Schedule
              </Button>
              <Button
                size="sm"
                onClick={handleNewAppointment}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Appointment
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions Toolbar */}
      {appointmentsState.bulkActionMode && (
        <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Edit className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h3 className="font-medium text-purple-900">Bulk Actions Mode</h3>
                <p className="text-sm text-purple-700">
                  {appointmentsState.selectedAppointments.length} appointment(s) selected
                </p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('confirm')}
                disabled={appointmentsState.selectedAppointments.length === 0}
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Confirm
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('reschedule')}
                disabled={appointmentsState.selectedAppointments.length === 0}
                className="border-blue-200 text-blue-700 hover:bg-blue-50"
              >
                <Calendar className="h-3 w-3 mr-1" />
                Reschedule
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('cancel')}
                disabled={appointmentsState.selectedAppointments.length === 0}
                className="border-orange-200 text-orange-700 hover:bg-orange-50"
              >
                <AlertCircle className="h-3 w-3 mr-1" />
                Cancel
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('delete')}
                disabled={appointmentsState.selectedAppointments.length === 0}
                className="border-red-200 text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="group hover:shadow-lg hover:shadow-blue-100/50 transition-all duration-300 hover:-translate-y-1 border-blue-100/50 bg-gradient-to-br from-blue-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors">
              Today&apos;s Appointments
            </CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
              <Calendar className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-blue-600">
                {statsLoading ? '...' : todaysAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                Today
              </Badge>
            </div>
            <p className="text-xs text-blue-600">
              {confirmedAppointments.filter(apt =>
                new Date(apt.scheduledAt).toDateString() === new Date().toDateString()
              ).length} confirmed
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-green-100/50 transition-all duration-300 hover:-translate-y-1 border-green-100/50 bg-gradient-to-br from-green-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-green-700 transition-colors">
              This Week
            </CardTitle>
            <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
              <Clock className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-green-600">
                {statsLoading ? '...' : thisWeekAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-green-200 text-green-700">
                7 days
              </Badge>
            </div>
            <p className="text-xs text-green-600">Scheduled appointments</p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-purple-100/50 transition-all duration-300 hover:-translate-y-1 border-purple-100/50 bg-gradient-to-br from-purple-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-purple-700 transition-colors">
              Upcoming
            </CardTitle>
            <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
              <TrendingUp className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-purple-600">
                {statsLoading ? '...' : upcomingAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-purple-200 text-purple-700">
                Future
              </Badge>
            </div>
            <p className="text-xs text-purple-600">Future appointments</p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-emerald-100/50 transition-all duration-300 hover:-translate-y-1 border-emerald-100/50 bg-gradient-to-br from-emerald-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-emerald-700 transition-colors">
              Completed
            </CardTitle>
            <div className="p-2 bg-emerald-100 rounded-lg group-hover:bg-emerald-200 transition-colors">
              <CheckCircle className="h-4 w-4 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-emerald-600">
                {statsLoading ? '...' : completedAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-emerald-200 text-emerald-700">
                Done
              </Badge>
            </div>
            <p className="text-xs text-emerald-600">Completed treatments</p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Appointments Views with Tabs */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <Tabs value={appointmentsState.activeView} onValueChange={(value) =>
          setAppointmentsState(prev => ({ ...prev, activeView: value as AppointmentsState['activeView'] }))
        } className="w-full">
          {/* Enhanced Tab Navigation */}
          <div className="border-b border-gray-200 bg-gradient-to-r from-blue-50/30 via-white to-green-50/30">
            <div className="px-6 py-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Appointment Management</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Comprehensive view of all your appointments
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-blue-50 hover:border-blue-200"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-green-50 hover:border-green-200"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>

              <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <List className="h-4 w-4" />
                  <span className="hidden sm:inline">Overview</span>
                </TabsTrigger>
                <TabsTrigger value="calendar" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span className="hidden sm:inline">Calendar</span>
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="hidden sm:inline">Analytics</span>
                </TabsTrigger>
              </TabsList>
            </div>
          </div>

          {/* Overview Tab - Main Appointment List */}
          <TabsContent value="overview" className="p-6">
            <DentistAppointmentList
              dentistId={dentistProfileId}
              onViewDetails={handleViewDetails}
              onUpdateStatus={handleStatusUpdate}
            />
          </TabsContent>

          {/* Calendar Tab */}
          <TabsContent value="calendar" className="p-6 space-y-6">
            <div className="text-center p-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
              <Calendar className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-blue-900 mb-2">Calendar View</h3>
              <p className="text-blue-700 mb-4">
                Interactive calendar view for appointment management
              </p>
              <p className="text-sm text-blue-600">
                Coming soon - Full calendar integration with drag-and-drop scheduling
              </p>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="p-6 space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="hover:shadow-lg transition-all duration-300 border-purple-100/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-purple-600" />
                    Appointment Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                      <div className="text-3xl font-bold text-purple-600 mb-2">Advanced Analytics</div>
                      <p className="text-sm text-purple-700">Coming Soon</p>
                      <p className="text-xs text-purple-600 mt-2">
                        Detailed appointment insights and trends
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-orange-100/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-orange-600" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg">
                      <div className="text-3xl font-bold text-orange-600 mb-2">Performance Insights</div>
                      <p className="text-sm text-orange-700">Coming Soon</p>
                      <p className="text-xs text-orange-600 mt-2">
                        Appointment efficiency and patient satisfaction metrics
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Enhanced Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={async () => {
          setConfirmDialog(prev => ({ ...prev, loading: true }))
          try {
            await confirmDialog.onConfirm()
            setConfirmDialog(prev => ({ ...prev, open: false, loading: false }))
          } catch (error) {
            console.error('Confirmation action failed:', error)
            toast.error('Action failed. Please try again.')
            setConfirmDialog(prev => ({ ...prev, loading: false }))
          }
        }}
        loading={confirmDialog.loading}
        variant={confirmDialog.variant}
        confirmText="Continue"
        cancelText="Cancel"
      />
    </div>
  )
}
