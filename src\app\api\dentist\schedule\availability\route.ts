import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole, TimeOffStatus, AppointmentStatus } from '@prisma/client'

// GET /api/dentist/schedule/availability - Enhanced availability checking
export async function GET(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const { searchParams } = new URL(request.url)
      const dentistId = searchParams.get('dentistId')
      const date = searchParams.get('date')
      const startDate = searchParams.get('startDate')
      const endDate = searchParams.get('endDate')
      const duration = parseInt(searchParams.get('duration') || '30') // Default 30 minutes

      if (!dentistId) {
        return NextResponse.json(
          { error: 'Dentist ID is required' },
          { status: 400 }
        )
      }

      if (!date && (!startDate || !endDate)) {
        return NextResponse.json(
          { error: 'Either date or startDate/endDate range is required' },
          { status: 400 }
        )
      }

      // Verify dentist exists
      const dentist = await prisma.dentistProfile.findUnique({
        where: { id: dentistId },
        include: {
          user: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      if (!dentist) {
        return NextResponse.json(
          { error: 'Dentist not found' },
          { status: 404 }
        )
      }

      // Authorization check - patients can only check availability, not detailed schedule
      const canViewDetails = [UserRole.DENTIST as string, UserRole.STAFF as string, UserRole.ADMIN as string].includes(user.role as string)

      // Determine date range
      const queryStartDate = date ? new Date(date) : new Date(startDate!)
      const queryEndDate = date ? new Date(date) : new Date(endDate!)

      // Set time boundaries for single date
      if (date) {
        queryStartDate.setHours(0, 0, 0, 0)
        queryEndDate.setHours(23, 59, 59, 999)
      }

      // Get working hours for the date range
      const workingHours = await prisma.dentistWorkingHours.findMany({
        where: {
          dentistId,
          isActive: true,
          effectiveFrom: { lte: queryEndDate },
          OR: [
            { effectiveTo: null },
            { effectiveTo: { gte: queryStartDate } }
          ]
        },
        orderBy: { effectiveFrom: 'desc' }
      })

      // Get time blocks for the date range
      const timeBlocks = await prisma.dentistTimeBlock.findMany({
        where: {
          dentistId,
          isActive: true,
          startTime: { gte: queryStartDate },
          endTime: { lte: queryEndDate }
        },
        orderBy: { startTime: 'asc' }
      })

      // Get time off for the date range
      const timeOff = await prisma.dentistTimeOff.findMany({
        where: {
          dentistId,
          status: TimeOffStatus.APPROVED,
          startDate: { lte: queryEndDate },
          endDate: { gte: queryStartDate }
        }
      })

      // Get existing appointments for the date range
      const appointments = await prisma.appointment.findMany({
        where: {
          dentistId,
          scheduledAt: {
            gte: queryStartDate,
            lte: queryEndDate
          },
          status: {
            in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED]
          }
        },
        orderBy: { scheduledAt: 'asc' },
        include: canViewDetails ? {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          },
          service: {
            select: {
              name: true,
              duration: true
            }
          }
        } : undefined
      })

      // Calculate availability
      const availability = calculateAvailability({
        queryStartDate,
        queryEndDate,
        workingHours,
        timeBlocks,
        timeOff,
        appointments,
        duration,
        canViewDetails
      })

      return NextResponse.json({
        success: true,
        data: {
          dentist: {
            id: dentist.id,
            name: dentist.user.name,
            email: dentist.user.email
          },
          dateRange: {
            startDate: queryStartDate,
            endDate: queryEndDate
          },
          availability,
          ...(canViewDetails && {
            workingHours,
            timeBlocks,
            timeOff,
            appointments
          })
        }
      })

    } catch (error) {
      console.error('Error checking availability:', error)
      return NextResponse.json(
        { error: 'Failed to check availability' },
        { status: 500 }
      )
    }
  }, [UserRole.PATIENT, UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}

// Helper function to calculate availability
function calculateAvailability({
  queryStartDate,
  queryEndDate,
  workingHours,
  timeBlocks,
  timeOff,
  appointments,
  duration,
  canViewDetails
}: {
  queryStartDate: Date
  queryEndDate: Date
  workingHours: any[]
  timeBlocks: any[]
  timeOff: any[]
  appointments: any[]
  duration: number
  canViewDetails: boolean
}) {
  const availableSlots: any[] = []
  const busySlots: any[] = []

  // Iterate through each day in the range
  const currentDate = new Date(queryStartDate)
  while (currentDate <= queryEndDate) {
    const dayOfWeek = currentDate.getDay()
    
    // Check if dentist is on time off
    const isOnTimeOff = timeOff.some(timeOffItem => {
      const startDate = new Date(timeOffItem.startDate)
      const endDate = new Date(timeOffItem.endDate)
      return currentDate >= startDate && currentDate <= endDate
    })

    if (isOnTimeOff) {
      if (canViewDetails) {
        busySlots.push({
          date: new Date(currentDate),
          type: 'time_off',
          reason: 'Time off'
        })
      }
      currentDate.setDate(currentDate.getDate() + 1)
      continue
    }

    // Get working hours for this day
    const dayWorkingHours = workingHours.find(wh => 
      wh.dayOfWeek === dayOfWeek &&
      new Date(wh.effectiveFrom) <= currentDate &&
      (!wh.effectiveTo || new Date(wh.effectiveTo) >= currentDate)
    )

    if (!dayWorkingHours) {
      // No working hours defined for this day
      currentDate.setDate(currentDate.getDate() + 1)
      continue
    }

    // Generate time slots for the day
    const daySlots = generateDaySlots({
      date: new Date(currentDate),
      workingHours: dayWorkingHours,
      timeBlocks: timeBlocks.filter(tb => {
        const blockDate = new Date(tb.startTime)
        return blockDate.toDateString() === currentDate.toDateString()
      }),
      appointments: appointments.filter(apt => {
        const aptDate = new Date(apt.scheduledAt)
        return aptDate.toDateString() === currentDate.toDateString()
      }),
      duration,
      canViewDetails
    })

    availableSlots.push(...daySlots.available)
    busySlots.push(...daySlots.busy)

    currentDate.setDate(currentDate.getDate() + 1)
  }

  return {
    availableSlots: availableSlots.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime()),
    ...(canViewDetails && {
      busySlots: busySlots.sort((a, b) => new Date(a.startTime || a.date).getTime() - new Date(b.startTime || b.date).getTime())
    })
  }
}

// Helper function to generate slots for a single day
function generateDaySlots({
  date,
  workingHours,
  timeBlocks,
  appointments,
  duration,
  canViewDetails
}: {
  date: Date
  workingHours: any
  timeBlocks: any[]
  appointments: any[]
  duration: number
  canViewDetails: boolean
}) {
  const available: any[] = []
  const busy: any[] = []

  // Parse working hours
  const [startHour, startMin] = workingHours.startTime.split(':').map(Number)
  const [endHour, endMin] = workingHours.endTime.split(':').map(Number)

  const dayStart = new Date(date)
  dayStart.setHours(startHour, startMin, 0, 0)
  
  const dayEnd = new Date(date)
  dayEnd.setHours(endHour, endMin, 0, 0)

  // Create busy periods from appointments, time blocks, and breaks
  const busyPeriods: { start: Date; end: Date; type: string; details?: any }[] = []

  // Add appointments
  appointments.forEach(apt => {
    const start = new Date(apt.scheduledAt)
    const end = new Date(start.getTime() + (apt.service?.duration || 30) * 60000)
    busyPeriods.push({
      start,
      end,
      type: 'appointment',
      details: canViewDetails ? apt : undefined
    })
  })

  // Add time blocks
  timeBlocks.forEach(block => {
    busyPeriods.push({
      start: new Date(block.startTime),
      end: new Date(block.endTime),
      type: 'time_block',
      details: canViewDetails ? block : undefined
    })
  })

  // Add lunch break
  if (workingHours.lunchStart && workingHours.lunchEnd) {
    const [lunchStartHour, lunchStartMin] = workingHours.lunchStart.split(':').map(Number)
    const [lunchEndHour, lunchEndMin] = workingHours.lunchEnd.split(':').map(Number)
    
    const lunchStart = new Date(date)
    lunchStart.setHours(lunchStartHour, lunchStartMin, 0, 0)
    
    const lunchEnd = new Date(date)
    lunchEnd.setHours(lunchEndHour, lunchEndMin, 0, 0)

    busyPeriods.push({
      start: lunchStart,
      end: lunchEnd,
      type: 'lunch',
    })
  }

  // Add break slots
  if (workingHours.breakSlots && Array.isArray(workingHours.breakSlots)) {
    workingHours.breakSlots.forEach((breakSlot: any) => {
      const [breakStartHour, breakStartMin] = breakSlot.startTime.split(':').map(Number)
      const [breakEndHour, breakEndMin] = breakSlot.endTime.split(':').map(Number)
      
      const breakStart = new Date(date)
      breakStart.setHours(breakStartHour, breakStartMin, 0, 0)
      
      const breakEnd = new Date(date)
      breakEnd.setHours(breakEndHour, breakEndMin, 0, 0)

      busyPeriods.push({
        start: breakStart,
        end: breakEnd,
        type: 'break',
        details: breakSlot
      })
    })
  }

  // Sort busy periods by start time
  busyPeriods.sort((a, b) => a.start.getTime() - b.start.getTime())

  // Generate available slots
  let currentTime = new Date(dayStart)
  const slotDuration = duration * 60000 // Convert to milliseconds

  while (currentTime.getTime() + slotDuration <= dayEnd.getTime()) {
    const slotEnd = new Date(currentTime.getTime() + slotDuration)
    
    // Check if this slot conflicts with any busy period
    const hasConflict = busyPeriods.some(busy => 
      (currentTime < busy.end && slotEnd > busy.start)
    )

    if (!hasConflict) {
      available.push({
        startTime: new Date(currentTime),
        endTime: new Date(slotEnd),
        duration
      })
      currentTime = new Date(currentTime.getTime() + 15 * 60000) // 15-minute intervals
    } else {
      // Find the next available time after the conflicting period
      const conflictingPeriod = busyPeriods.find(busy => 
        (currentTime < busy.end && slotEnd > busy.start)
      )
      if (conflictingPeriod) {
        currentTime = new Date(Math.max(conflictingPeriod.end.getTime(), currentTime.getTime() + 15 * 60000))
      } else {
        currentTime = new Date(currentTime.getTime() + 15 * 60000)
      }
    }
  }

  // Add busy periods to the response if details are allowed
  if (canViewDetails) {
    busyPeriods.forEach(period => {
      busy.push({
        startTime: period.start,
        endTime: period.end,
        type: period.type,
        details: period.details
      })
    })
  }

  return { available, busy }
}
