'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { UserRole } from '@prisma/client'
import { toast, Toaster } from 'sonner'
import { withErrorHandling, showSuccessMessage, showInfoMessage } from '@/lib/error-handling'
import {
  useWorkingHours,
  useTimeBlocks,
  useTimeOff,
  useScheduleTemplates
} from '@/hooks/useScheduleAPI'
import type {
  TimeBlockFormData,
  TimeOffFormData
} from '@/lib/validations/schedule'
import type {
  WorkingHoursCreateInput
} from '@/lib/validations'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import {
  Clock,
  Calendar,
  Settings,
  Plus,
  Edit,
  Loader2,
  CalendarDays,
  Timer,
  Repeat,
  Plane,
  ChevronRight,
  Home,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Eye,
  Grid3X3,
  List,
  Filter,
  Bell,
  Link
} from 'lucide-react'

// Import our new schedule components
import {
  WeeklyScheduleGrid,
  TimeSlotPicker,
  RecurringPatternSelector,
  HolidayCalendar,
  TimeBlockingInterface
} from '@/components/schedule'



// Enhanced schedule view types
type ScheduleView = 'daily' | 'weekly' | 'monthly'
type ScheduleMode = 'view' | 'edit' | 'create'

// Enhanced schedule state interface
interface ScheduleState {
  view: ScheduleView
  mode: ScheduleMode
  selectedDate: Date
  selectedTimeSlot?: any
  isLoading: boolean
  hasUnsavedChanges: boolean
}

export default function DentistSchedule() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // Enhanced state management
  const [scheduleState, setScheduleState] = useState<ScheduleState>({
    view: 'weekly',
    mode: 'view',
    selectedDate: new Date(),
    isLoading: false,
    hasUnsavedChanges: false
  })

  const [activeTab, setActiveTab] = useState('overview')
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Dentist profile state
  const [dentistProfile, setDentistProfile] = useState<{ id: string } | null>(null)

  // Enhanced confirmation dialog state
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean
    title: string
    description: string
    onConfirm: () => Promise<void>
    loading: boolean
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: async () => {},
    loading: false
  })

  // API hooks for schedule management
  const workingHours = useWorkingHours()
  const timeBlocks = useTimeBlocks()
  const timeOff = useTimeOff()
  const scheduleTemplates = useScheduleTemplates()

  // Enhanced state update function
  const updateScheduleState = useCallback((updates: Partial<ScheduleState>) => {
    setScheduleState(prev => ({ ...prev, ...updates }))
  }, [])

  // Destructure fetch functions for stable references
  const { fetchWorkingHours } = workingHours
  const { fetchTimeBlocks } = timeBlocks
  const { fetchTimeOff } = timeOff
  const { fetchTemplates } = scheduleTemplates

  // Refresh all schedule data
  const refreshScheduleData = useCallback(async () => {
    await withErrorHandling(
      async () => {
        await Promise.all([
          workingHours.fetchWorkingHours(),
          timeBlocks.fetchTimeBlocks(),
          timeOff.fetchTimeOff()
        ])
      },
      'Refresh Schedule Data',
      refreshScheduleData
    )
  }, [workingHours, timeBlocks, timeOff])

  // Transform real data into schedule format
  const transformScheduleData = useCallback(() => {
    const scheduleData: Array<{
      date: Date
      dayOfWeek: number
      isWorkingDay: boolean
      timeSlots: Array<{
        id: string
        startTime: string
        endTime: string
        title: string
        type: 'working' | 'break' | 'blocked' | 'appointment'
        description?: string
      }>
    }> = []

    const currentWeekStart = new Date(scheduleState.selectedDate)
    currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay())

    // Generate 7 days starting from the week start
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(currentWeekStart)
      currentDate.setDate(currentWeekStart.getDate() + i)
      const dayOfWeek = currentDate.getDay()

      // Find working hours for this day
      const dayWorkingHours = workingHours.data.filter(wh =>
        wh.dayOfWeek === dayOfWeek && wh.isActive
      )

      // Find time blocks for this specific date
      const dayTimeBlocks = timeBlocks.data.filter(tb => {
        const blockDate = new Date(tb.date)
        return blockDate.toDateString() === currentDate.toDateString()
      })

      // Find time off for this date
      const dayTimeOff = timeOff.data.filter(to => {
        const startDate = new Date(to.startDate)
        const endDate = new Date(to.endDate)
        return currentDate >= startDate && currentDate <= endDate
      })

      const timeSlots: Array<{
        id: string
        startTime: string
        endTime: string
        title: string
        type: 'working' | 'break' | 'blocked' | 'appointment'
        description?: string
      }> = []

      // Add working hours as time slots
      dayWorkingHours.forEach(wh => {
        timeSlots.push({
          id: wh.id,
          startTime: wh.startTime,
          endTime: wh.endTime,
          title: 'Available Hours',
          type: 'working' as const,
          description: 'Regular working hours'
        })
      })

      // Add time blocks
      dayTimeBlocks.forEach(tb => {
        timeSlots.push({
          id: tb.id,
          startTime: tb.startTime,
          endTime: tb.endTime,
          title: tb.title,
          type: tb.type === 'break' ? 'break' as const : 'blocked' as const,
          description: tb.description
        })
      })

      // Add time off (if not all day, use specific times)
      dayTimeOff.forEach(to => {
        if (!to.isAllDay && to.startTime && to.endTime) {
          timeSlots.push({
            id: to.id,
            startTime: to.startTime,
            endTime: to.endTime,
            title: to.title,
            type: 'blocked' as const,
            description: to.description
          })
        } else if (to.isAllDay) {
          timeSlots.push({
            id: to.id,
            startTime: '00:00',
            endTime: '23:59',
            title: to.title,
            type: 'blocked' as const,
            description: to.description
          })
        }
      })

      scheduleData.push({
        date: currentDate,
        dayOfWeek,
        isWorkingDay: dayWorkingHours.length > 0 && dayTimeOff.length === 0,
        timeSlots: timeSlots.sort((a, b) => a.startTime.localeCompare(b.startTime))
      })
    }

    return scheduleData
  }, [scheduleState.selectedDate, workingHours.data, timeBlocks.data, timeOff.data])

  // Load dentist profile first
  useEffect(() => {
    if (isAuthenticated && user?.role === UserRole.DENTIST) {
      const loadDentistProfile = async () => {
        try {
          const response = await fetch('/api/dentists/profile')
          if (response.ok) {
            const profile = await response.json()
            setDentistProfile({ id: profile.id })
          } else {
            console.error('Failed to load dentist profile')
          }
        } catch (error) {
          console.error('Error loading dentist profile:', error)
        }
      }
      loadDentistProfile()
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      // Redirect non-dentist users
      router.push('/dashboard')
    }
  }, [isAuthenticated, user?.role, router])

  // Load initial data after profile is loaded
  useEffect(() => {
    if (isAuthenticated && user?.role === UserRole.DENTIST && dentistProfile?.id) {
      // Add error handling to prevent infinite loops
      const loadData = async () => {
        try {
          await Promise.all([
            fetchWorkingHours(),
            fetchTimeBlocks(),
            fetchTimeOff(),
            fetchTemplates()
          ])
        } catch (error) {
          console.error('Failed to load schedule data:', error)
          // Don't retry automatically to prevent infinite loops
        }
      }
      loadData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isAuthenticated,
    user?.role,
    dentistProfile?.id
    // Remove the fetch functions from dependencies to prevent infinite loops
  ])

  // API handler functions
  const handleAddTimeSlot = async (date: Date, time: string) => {
    if (!dentistProfile?.id) {
      console.error('Dentist profile not loaded')
      return
    }

    await withErrorHandling(
      async () => {
        // Calculate end time (default to 1 hour later)
        const startDate = new Date(`2000-01-01T${time}:00`)
        const endDate = new Date(startDate.getTime() + 60 * 60 * 1000) // Add 1 hour
        const endTime = endDate.toTimeString().slice(0, 5) // Format as HH:MM

        const timeSlotData: WorkingHoursCreateInput = {
          dentistId: dentistProfile.id,
          dayOfWeek: date.getDay(),
          startTime: time,
          endTime: endTime,
        }
        await workingHours.createWorkingHour(timeSlotData)
        showSuccessMessage('Working hours added successfully')

        // Refresh schedule data
        await refreshScheduleData()
      },
      'Add Working Hours',
      () => handleAddTimeSlot(date, time)
    )
  }

  const handleAddTimeBlock = async (block: any) => {
    await withErrorHandling(
      async () => {
        const timeBlockData: TimeBlockFormData = {
          title: block.title,
          startTime: block.startTime,
          endTime: block.endTime,
          date: block.date,
          type: block.type,
          description: block.description,
          isRecurring: block.isRecurring || false
        }
        await timeBlocks.createTimeBlock(timeBlockData)
        showSuccessMessage('Time block added successfully')

        // Refresh schedule data
        await refreshScheduleData()
      },
      'Add Time Block',
      () => handleAddTimeBlock(block)
    )
  }

  const handleAddHoliday = async (holiday: any) => {
    await withErrorHandling(
      async () => {
        const holidayData: TimeOffFormData = {
          startDate: holiday.date,
          endDate: holiday.date,
          type: 'personal',
          title: holiday.name,
          description: holiday.description,
          isAllDay: true
        }
        await timeOff.createTimeOff(holidayData)
        showSuccessMessage('Holiday added successfully')

        // Refresh schedule data
        await refreshScheduleData()
      },
      'Add Holiday',
      () => handleAddHoliday(holiday)
    )
  }

  // State to store the current pattern for saving later
  const [currentPattern, setCurrentPattern] = useState<any>(null)

  const handlePatternChange = (pattern: any) => {
    // Only store the pattern, don't show success message
    setCurrentPattern(pattern)
    console.log('Pattern changed:', pattern)
  }

  const handleEditTimeSlot = async (slot: any) => {
    try {
      // For working hours slots, open edit dialog
      if (slot.type === 'working' && slot.id) {
        setConfirmDialog({
          open: true,
          title: 'Edit Working Hours',
          description: `Edit working hours for ${slot.title || 'this time slot'}. You can modify the start and end times.`,
          onConfirm: async () => {
            // This would open a time picker dialog in a real implementation
            // For now, we'll show a success message
            toast.success('Working hours edit dialog would open here')
            console.log('Edit working hours slot:', slot)
          },
          loading: false
        })
      } else {
        toast.info('Edit functionality available for working hours only')
        console.log('Edit slot:', slot)
      }
    } catch (error) {
      console.error('Failed to edit time slot:', error)
      toast.error('Failed to open edit dialog')
    }
  }

  const handleDeleteTimeSlot = async (slot: any) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Time Slot',
      description: `Are you sure you want to delete the time slot "${slot.title || 'Untitled'}"? This action cannot be undone.`,
      loading: false,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }))
        await withErrorHandling(
          async () => {
            // Delete based on slot type
            if (slot.type === 'working' && slot.id) {
              await workingHours.deleteWorkingHour(slot.id)
              showSuccessMessage('Working hours deleted successfully')
            } else if (slot.type === 'blocked' && slot.id) {
              await timeBlocks.deleteTimeBlock(slot.id)
              showSuccessMessage('Time block deleted successfully')
            } else if (slot.type === 'break' && slot.id) {
              await timeOff.deleteTimeOff(slot.id)
              showSuccessMessage('Time off deleted successfully')
            } else {
              showInfoMessage('Cannot delete this type of time slot')
              return
            }

            // Refresh schedule data
            await refreshScheduleData()
            console.log('Delete slot:', slot)
          },
          'Delete Time Slot',
          () => handleDeleteTimeSlot(slot)
        )

        setConfirmDialog(prev => ({ ...prev, loading: false }))
      }
    })
  }

  const handleEditTimeBlock = async (block: any) => {
    try {
      setConfirmDialog({
        open: true,
        title: 'Edit Time Block',
        description: `Edit the time block "${block.title || 'Untitled'}". You can modify the title, time, and description.`,
        onConfirm: async () => {
          // In a real implementation, this would open a form dialog
          // For now, we'll simulate an update
          if (block.id) {
            await timeBlocks.updateTimeBlock(block.id, {
              title: block.title,
              description: block.description,
              startTime: block.startTime,
              endTime: block.endTime,
              type: block.type || 'procedure',
              date: new Date(block.date || scheduleState.selectedDate),
              isRecurring: block.isRecurring || false
            })
            toast.success('Time block updated successfully')
          } else {
            toast.info('Time block edit form would open here')
          }
          console.log('Edit time block:', block)
        },
        loading: false
      })
    } catch (error) {
      console.error('Failed to edit time block:', error)
      toast.error('Failed to open edit dialog')
    }
  }

  const handleDeleteTimeBlock = async (blockId: string) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Time Block',
      description: 'Are you sure you want to delete this time block? This action cannot be undone.',
      loading: false,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }))
        try {
          await timeBlocks.deleteTimeBlock(blockId)
          toast.success('Time block deleted successfully')

          // Refresh schedule data
          await refreshScheduleData()
        } catch (error) {
          toast.error('Failed to delete time block')
          console.error('Failed to delete time block:', error)
        } finally {
          setConfirmDialog(prev => ({ ...prev, loading: false }))
        }
      }
    })
  }

  const handleEditHoliday = async (holiday: any) => {
    try {
      setConfirmDialog({
        open: true,
        title: 'Edit Time Off',
        description: `Edit the time off entry "${holiday.title || 'Untitled'}". You can modify the dates, title, and reason.`,
        onConfirm: async () => {
          // In a real implementation, this would open a date picker dialog
          if (holiday.id) {
            await timeOff.updateTimeOff(holiday.id, {
              title: holiday.title,
              description: holiday.reason || holiday.description,
              startDate: new Date(holiday.startDate),
              endDate: new Date(holiday.endDate),
              type: holiday.type || 'vacation',
              isAllDay: holiday.isAllDay !== false
            })
            toast.success('Time off updated successfully')
          } else {
            toast.info('Time off edit form would open here')
          }
          console.log('Edit holiday:', holiday)
        },
        loading: false
      })
    } catch (error) {
      console.error('Failed to edit holiday:', error)
      toast.error('Failed to open edit dialog')
    }
  }

  const handleDeleteHoliday = async (holidayId: string) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Holiday',
      description: 'Are you sure you want to delete this holiday? This action cannot be undone.',
      loading: false,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }))
        try {
          await timeOff.deleteTimeOff(holidayId)
          toast.success('Holiday deleted successfully')

          // Refresh schedule data
          await refreshScheduleData()
        } catch (error) {
          toast.error('Failed to delete holiday')
          console.error('Failed to delete holiday:', error)
        } finally {
          setConfirmDialog(prev => ({ ...prev, loading: false }))
        }
      }
    })
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/schedule')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  // Enhanced error state handling with better UX
  if (workingHours.error?.includes('Dentist profile not found') ||
      timeBlocks.error?.includes('Dentist profile not found') ||
      timeOff.error?.includes('Dentist profile not found') ||
      scheduleTemplates.error?.includes('Dentist profile not found')) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
        <Card className="max-w-lg mx-auto shadow-lg border-red-200">
          <CardHeader className="text-center pb-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-600">Profile Setup Required</CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Complete your dentist profile to access schedule management
            </p>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="bg-red-50 rounded-lg p-4 border border-red-200">
              <p className="text-sm text-red-700">
                Your dentist profile is not set up properly. Please contact your administrator
                to complete your profile setup before accessing schedule management.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={() => router.push('/dentist/dashboard')}
                className="flex-1"
              >
                <Home className="h-4 w-4 mr-2" />
                Return to Dashboard
              </Button>
              <Button
                onClick={() => {
                  // Retry loading data
                  fetchWorkingHours()
                  fetchTimeBlocks()
                  fetchTimeOff()
                  fetchTemplates()
                  toast.info('Retrying data load...')
                }}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Loading
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-8">
      {/* Enhanced Header with Breadcrumb and Status */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-transparent to-green-600/5 rounded-2xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-blue-100/50">
          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/dentist/dashboard')}
              className="p-0 h-auto font-normal hover:text-blue-600"
            >
              <Home className="h-4 w-4 mr-1" />
              Dashboard
            </Button>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900 font-medium">Schedule Management</span>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                    Schedule Management
                  </h1>
                  <p className="text-gray-600">
                    Manage your working hours, availability, and time blocks
                  </p>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="flex items-center gap-4 mt-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-600">Schedule Active</span>
                </div>
                {scheduleState.hasUnsavedChanges && (
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                    <span className="text-sm text-orange-600">Unsaved Changes</span>
                  </div>
                )}
                <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                  {scheduleState.view.charAt(0).toUpperCase() + scheduleState.view.slice(1)} View
                </Badge>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Refresh all data
                  Promise.all([
                    fetchWorkingHours(),
                    fetchTimeBlocks(),
                    fetchTimeOff(),
                    fetchTemplates()
                  ]).then(() => {
                    toast.success('Schedule data refreshed')
                  }).catch(() => {
                    toast.error('Failed to refresh data')
                  })
                }}
                className="hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => updateScheduleState({ view: scheduleState.view === 'weekly' ? 'daily' : 'weekly' })}
                className="hover:bg-green-50 hover:border-green-200 transition-all duration-200"
              >
                <Eye className="h-4 w-4 mr-2" />
                Switch View
              </Button>

              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Quick Block
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <Timer className="h-5 w-5 text-blue-600" />
                      Block Time Slot
                    </DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <TimeSlotPicker
                      title="Block Time"
                      description="Block time for procedures or personal use"
                      allowCustomTitle={true}
                      onSave={async (data) => {
                        await handleAddTimeBlock({
                          ...data,
                          date: scheduleState.selectedDate,
                          type: 'procedure'
                        })
                        setIsDialogOpen(false)
                        toast.success('Time slot blocked successfully')
                      }}
                      showActions={true}
                      loading={timeBlocks.loading}
                    />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Responsive Schedule Overview Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
        <Card className="group hover:shadow-lg hover:shadow-blue-100/50 transition-all duration-300 hover:scale-[1.02] sm:hover:-translate-y-1 border-blue-100/50 bg-gradient-to-br from-blue-50/50 to-white cursor-pointer"
              onClick={() => setActiveTab('overview')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-xs sm:text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors">
              Today&apos;s Hours
            </CardTitle>
            <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-1 sm:space-y-2 pt-0">
            <div className="flex items-baseline gap-1 sm:gap-2">
              <div className="text-lg sm:text-2xl font-bold text-blue-600">9:00</div>
              <span className="text-xs sm:text-sm text-gray-500">-</span>
              <div className="text-lg sm:text-2xl font-bold text-blue-600">5:00</div>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-blue-700">
                <span className="hidden sm:inline">8 hours </span>scheduled
              </p>
              <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                Active
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-green-100/50 transition-all duration-300 hover:scale-[1.02] sm:hover:-translate-y-1 border-green-100/50 bg-gradient-to-br from-green-50/50 to-white cursor-pointer"
              onClick={() => setActiveTab('overview')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-xs sm:text-sm font-medium text-gray-700 group-hover:text-green-700 transition-colors">
              Available Slots
            </CardTitle>
            <div className="p-1.5 sm:p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-1 sm:space-y-2 pt-0">
            <div className="flex items-baseline gap-1 sm:gap-2">
              <div className="text-2xl sm:text-3xl font-bold text-green-600">4</div>
              <span className="text-xs sm:text-sm text-gray-500">slots</span>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-green-700">
                <span className="hidden sm:inline">Open appointments </span>today
              </p>
              <Badge variant="outline" className="text-xs border-green-200 text-green-700">
                Available
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-purple-100/50 transition-all duration-300 hover:scale-[1.02] sm:hover:-translate-y-1 border-purple-100/50 bg-gradient-to-br from-purple-50/50 to-white cursor-pointer"
              onClick={() => setActiveTab('timeblocks')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-xs sm:text-sm font-medium text-gray-700 group-hover:text-purple-700 transition-colors">
              Time Blocks
            </CardTitle>
            <div className="p-1.5 sm:p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
              <Timer className="h-3 w-3 sm:h-4 sm:w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-1 sm:space-y-2 pt-0">
            <div className="flex items-baseline gap-1 sm:gap-2">
              <div className="text-2xl sm:text-3xl font-bold text-purple-600">2</div>
              <span className="text-xs sm:text-sm text-gray-500">blocks</span>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-purple-700">
                <span className="hidden sm:inline">Blocked slots </span>today
              </p>
              <Badge variant="outline" className="text-xs border-purple-200 text-purple-700">
                Blocked
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-orange-100/50 transition-all duration-300 hover:scale-[1.02] sm:hover:-translate-y-1 border-orange-100/50 bg-gradient-to-br from-orange-50/50 to-white cursor-pointer"
              onClick={() => setActiveTab('recurring')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-xs sm:text-sm font-medium text-gray-700 group-hover:text-orange-700 transition-colors">
              Weekly Hours
            </CardTitle>
            <div className="p-1.5 sm:p-2 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors">
              <CalendarDays className="h-3 w-3 sm:h-4 sm:w-4 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-1 sm:space-y-2 pt-0">
            <div className="flex items-baseline gap-1 sm:gap-2">
              <div className="text-2xl sm:text-3xl font-bold text-orange-600">40</div>
              <span className="text-xs sm:text-sm text-gray-500">hours</span>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-orange-700">
                <span className="hidden sm:inline">Hours this </span>week
              </p>
              <Badge variant="outline" className="text-xs border-orange-200 text-orange-700">
                On Track
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Schedule Management Interface */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Enhanced Responsive Tab Navigation */}
          <div className="border-b border-gray-200 bg-gradient-to-r from-blue-50/30 via-white to-green-50/30">
            <div className="px-3 sm:px-6 py-4">
              {/* Mobile-First Header */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <div>
                  <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Schedule Views</h2>
                  <p className="text-xs sm:text-sm text-gray-600 mt-1">
                    Manage your schedule across different time periods
                  </p>
                </div>

                {/* Responsive View Controls */}
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex items-center bg-white rounded-lg border border-gray-200 p-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => updateScheduleState({ view: 'daily' })}
                      className={`px-2 sm:px-3 transition-all duration-200 ${
                        scheduleState.view === 'daily'
                          ? 'bg-blue-600 text-white shadow-sm'
                          : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700'
                      }`}
                    >
                      <Grid3X3 className="h-4 w-4 sm:mr-1" />
                      <span className="hidden sm:inline">Daily</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => updateScheduleState({ view: 'weekly' })}
                      className={`px-2 sm:px-3 transition-all duration-200 ${
                        scheduleState.view === 'weekly'
                          ? 'bg-blue-600 text-white shadow-sm'
                          : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700'
                      }`}
                    >
                      <List className="h-4 w-4 sm:mr-1" />
                      <span className="hidden sm:inline">Weekly</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => updateScheduleState({ view: 'monthly' })}
                      className={`px-2 sm:px-3 transition-all duration-200 ${
                        scheduleState.view === 'monthly'
                          ? 'bg-blue-600 text-white shadow-sm'
                          : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700'
                      }`}
                    >
                      <Calendar className="h-4 w-4 sm:mr-1" />
                      <span className="hidden sm:inline">Monthly</span>
                    </Button>
                  </div>

                  <Separator orientation="vertical" className="h-6 hidden sm:block" />

                  <Button
                    variant="ghost"
                    size="sm"
                    className="px-2 sm:px-3 text-gray-600 hover:bg-gray-50 hover:text-gray-700 transition-all duration-200"
                  >
                    <Filter className="h-4 w-4 sm:mr-1" />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                </div>
              </div>

              {/* Enhanced Responsive TabsList */}
              <div className="w-full overflow-x-auto">
                <TabsList className="grid w-full min-w-[500px] grid-cols-5 bg-white border border-gray-200 p-1 rounded-lg shadow-sm">
                  <TabsTrigger
                    value="overview"
                    className="flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-blue-50 text-xs sm:text-sm"
                  >
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Schedule</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="timeblocks"
                    className="flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-purple-50 text-xs sm:text-sm"
                  >
                    <Timer className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Blocks</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="recurring"
                    className="flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 data-[state=active]:bg-green-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-green-50 text-xs sm:text-sm"
                  >
                    <Repeat className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Patterns</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="holidays"
                    className="flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 data-[state=active]:bg-orange-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-orange-50 text-xs sm:text-sm"
                  >
                    <Plane className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Time Off</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="settings"
                    className="flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 data-[state=active]:bg-gray-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 hover:bg-gray-50 text-xs sm:text-sm"
                  >
                    <Settings className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Settings</span>
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>
          </div>

          <TabsContent value="overview" className="p-6 space-y-6">
            {/* Responsive Schedule View Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="space-y-1">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
                  {scheduleState.view.charAt(0).toUpperCase() + scheduleState.view.slice(1)} Schedule View
                </h3>
                <p className="text-sm text-gray-600">
                  {scheduleState.view === 'weekly' ? 'View and manage your weekly schedule' :
                   scheduleState.view === 'daily' ? 'View and manage your daily schedule' :
                   'View and manage your monthly schedule'}
                </p>
              </div>

              <div className="flex flex-wrap items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateScheduleState({ mode: scheduleState.mode === 'edit' ? 'view' : 'edit' })}
                  className={`transition-all duration-200 ${
                    scheduleState.mode === 'edit'
                      ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <Edit className="h-4 w-4 sm:mr-2" />
                  <span className="hidden sm:inline">
                    {scheduleState.mode === 'edit' ? 'View Mode' : 'Edit Mode'}
                  </span>
                </Button>

                {scheduleState.hasUnsavedChanges && (
                  <Badge
                    variant="outline"
                    className="text-orange-600 border-orange-200 bg-orange-50 animate-pulse"
                  >
                    <AlertCircle className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Unsaved Changes</span>
                    <span className="sm:hidden">Unsaved</span>
                  </Badge>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    fetchWorkingHours()
                    toast.info('Schedule refreshed')
                  }}
                  className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                >
                  <RefreshCw className="h-4 w-4 sm:mr-2" />
                  <span className="hidden sm:inline">Refresh</span>
                </Button>
              </div>
            </div>

            {workingHours.loading || scheduleState.isLoading ? (
              <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-medium text-gray-900">Loading Schedule</h4>
                  <p className="text-sm text-gray-600">Please wait while we fetch your schedule data...</p>
                </div>
              </div>
            ) : workingHours.error ? (
              <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-medium text-gray-900">Error Loading Schedule</h4>
                  <p className="text-sm text-gray-600">{workingHours.error}</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      fetchWorkingHours()
                      toast.info('Retrying...')
                    }}
                    className="mt-4"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {scheduleState.view === 'weekly' && (
                  <WeeklyScheduleGrid
                    weekStart={scheduleState.selectedDate}
                    schedule={transformScheduleData()}
                    onAddTimeSlot={handleAddTimeSlot}
                    onEditTimeSlot={handleEditTimeSlot}
                    onDeleteTimeSlot={handleDeleteTimeSlot}
                    className="min-h-[600px]"
                    readOnly={scheduleState.mode === 'view'}
                  />
                )}

                {scheduleState.view === 'daily' && (
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <div className="text-center space-y-4">
                      <Calendar className="h-12 w-12 text-blue-600 mx-auto" />
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">Daily View</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          Daily schedule view for {scheduleState.selectedDate.toLocaleDateString()}
                        </p>
                      </div>
                      <div className="bg-blue-50 rounded-lg p-4">
                        <p className="text-sm text-blue-700">
                          Daily view component will be implemented in the next phase
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {scheduleState.view === 'monthly' && (
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <div className="text-center space-y-4">
                      <CalendarDays className="h-12 w-12 text-blue-600 mx-auto" />
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">Monthly View</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          Monthly schedule overview for {scheduleState.selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                        </p>
                      </div>
                      <div className="bg-blue-50 rounded-lg p-4">
                        <p className="text-sm text-blue-700">
                          Monthly view component will be implemented in the next phase
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Enhanced Action Bar */}
                {scheduleState.mode === 'edit' && (
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm font-medium text-gray-900">Edit Mode Active</span>
                        <span className="text-xs text-gray-600">Click on time slots to modify your schedule</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            updateScheduleState({ hasUnsavedChanges: false, mode: 'view' })
                            fetchWorkingHours()
                            toast.info('Changes discarded')
                          }}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Discard Changes
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            updateScheduleState({ hasUnsavedChanges: false, mode: 'view' })
                            toast.success('Schedule saved successfully')
                          }}
                          disabled={workingHours.loading}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {workingHours.loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="timeblocks" className="p-6 space-y-6">
            {/* Time Blocks Header */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Time Block Management</h3>
                <p className="text-sm text-gray-600">
                  Block time for procedures, breaks, or personal appointments
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    fetchTimeBlocks()
                    toast.info('Time blocks refreshed')
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  size="sm"
                  onClick={() => setIsDialogOpen(true)}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Block
                </Button>
              </div>
            </div>

            {timeBlocks.loading ? (
              <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-medium text-gray-900">Loading Time Blocks</h4>
                  <p className="text-sm text-gray-600">Please wait while we fetch your time blocks...</p>
                </div>
              </div>
            ) : timeBlocks.error ? (
              <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-medium text-gray-900">Error Loading Time Blocks</h4>
                  <p className="text-sm text-gray-600">{timeBlocks.error}</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      fetchTimeBlocks()
                      toast.info('Retrying...')
                    }}
                    className="mt-4"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <TimeBlockingInterface
                  timeBlocks={timeBlocks.data}
                  selectedDate={scheduleState.selectedDate}
                  onAddTimeBlock={handleAddTimeBlock}
                  onEditTimeBlock={handleEditTimeBlock}
                  onDeleteTimeBlock={handleDeleteTimeBlock}
                />

                {/* Bulk Actions */}
                <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Timer className="h-5 w-5 text-purple-600" />
                      <span className="text-sm font-medium text-gray-900">Bulk Operations</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setConfirmDialog({
                            open: true,
                            title: 'Clear All Time Blocks',
                            description: 'Are you sure you want to remove all time blocks for today? This action cannot be undone.',
                            onConfirm: async () => {
                              // Implement bulk delete
                              toast.success('All time blocks cleared')
                            },
                            loading: false
                          })
                        }}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        Clear All
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Implement copy from previous day
                          toast.success('Time blocks copied from previous day')
                        }}
                      >
                        Copy Previous Day
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="recurring" className="p-6 space-y-6">
            {/* Recurring Patterns Header */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Recurring Schedule Patterns</h3>
                <p className="text-sm text-gray-600">
                  Set up recurring patterns for your regular working hours and manage templates
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    fetchTemplates()
                    toast.info('Templates refreshed')
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    // Open template creation dialog
                    setConfirmDialog({
                      open: true,
                      title: 'Create New Template',
                      description: 'Create a new schedule template from your current pattern configuration.',
                      onConfirm: async () => {
                        if (!currentPattern) {
                          toast.error('Please configure a pattern first')
                          return
                        }
                        // Template creation logic will be handled in the existing save handler
                        toast.success('Template creation started')
                      },
                      loading: false
                    })
                  }}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Template
                </Button>
              </div>
            </div>

            {/* Pattern Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Repeat className="h-5 w-5 text-green-600" />
                  Pattern Configuration
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Configure your recurring schedule pattern
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <RecurringPatternSelector
                  onChange={handlePatternChange}
                  className="max-w-2xl"
                />

                {/* Pattern Preview */}
                {currentPattern && (
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <h4 className="font-medium text-green-900 mb-2">Pattern Preview</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Type:</span>
                        <span className="ml-2 font-medium text-green-700">
                          {currentPattern.type?.charAt(0).toUpperCase() + currentPattern.type?.slice(1)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Time:</span>
                        <span className="ml-2 font-medium text-green-700">
                          {currentPattern.startTime} - {currentPattern.endTime}
                        </span>
                      </div>
                      <div className="col-span-2">
                        <span className="text-gray-600">Days:</span>
                        <div className="flex gap-1 mt-1">
                          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                            <Badge
                              key={day}
                              variant={currentPattern.daysOfWeek?.includes(index) ? "default" : "outline"}
                              className={`text-xs ${
                                currentPattern.daysOfWeek?.includes(index)
                                  ? 'bg-green-600 text-white'
                                  : 'text-gray-500'
                              }`}
                            >
                              {day}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Conflict Detection */}
                {currentPattern && (
                  <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-orange-600" />
                      <h4 className="font-medium text-orange-900">Conflict Detection</h4>
                    </div>
                    <p className="text-sm text-orange-700 mb-3">
                      Checking for conflicts with existing appointments and time blocks...
                    </p>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-700">No conflicts detected</span>
                    </div>
                  </div>
                )}

              {/* Save/Cancel Actions */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    // Reset form logic would go here
                    toast.info('Pattern changes cancelled')
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={async () => {
                    try {
                      if (!currentPattern) {
                        toast.error('Please configure a pattern first')
                        return
                      }

                      if (!user) {
                        toast.error('User not authenticated')
                        return
                      }

                      // Get dentist profile
                      const dentistResponse = await fetch('/api/dentists/profile')
                      if (!dentistResponse.ok) {
                        throw new Error('Failed to get dentist profile')
                      }
                      const dentistData = await dentistResponse.json()
                      const dentistId = dentistData.data?.id

                      if (!dentistId) {
                        throw new Error('Dentist profile not found')
                      }

                      // Convert pattern to working hours format
                      const workingHours = currentPattern.daysOfWeek?.map((dayOfWeek: number) => ({
                        dayOfWeek,
                        startTime: currentPattern.startTime || '09:00',
                        endTime: currentPattern.endTime || '17:00',
                        isActive: true
                      })) || []

                      // Create template with proper structure
                      const templateData = {
                        name: `${currentPattern.type.charAt(0).toUpperCase() + currentPattern.type.slice(1)} Pattern`,
                        description: `Auto-generated from ${currentPattern.type} recurring pattern`,
                        workingHours,
                        timeBlocks: [],
                        isDefault: false
                      }

                      await scheduleTemplates.createTemplate(templateData)
                      toast.success('Pattern saved successfully')
                    } catch (error) {
                      console.error('Failed to save pattern:', error)
                      toast.error('Failed to save pattern')
                    }
                  }}
                  disabled={scheduleTemplates.loading || !currentPattern}
                >
                  {scheduleTemplates.loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Save Pattern
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

          <TabsContent value="holidays" className="p-6 space-y-6">
            {/* Time Off Header */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Time Off & Holidays</h3>
                <p className="text-sm text-gray-600">
                  Manage your vacation days, holidays, and unavailable periods
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    fetchTimeOff()
                    toast.info('Time off data refreshed')
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    // Open add holiday dialog
                    setConfirmDialog({
                      open: true,
                      title: 'Add Time Off',
                      description: 'Add a new vacation day or holiday to your schedule. This will create a time off entry for the selected date.',
                      onConfirm: async () => {
                        if (!dentistProfile) {
                          toast.error('Dentist profile not loaded')
                          return
                        }

                        try {
                          // Create a time off entry for the selected date
                          await timeOff.createTimeOff({
                            title: 'Time Off',
                            description: 'Personal time off',
                            startDate: scheduleState.selectedDate,
                            endDate: scheduleState.selectedDate,
                            type: 'personal',
                            isAllDay: true
                          })
                          toast.success('Time off added successfully')

                          // Refresh the schedule data
                          await Promise.all([
                            workingHours.fetchWorkingHours(),
                            timeBlocks.fetchTimeBlocks(),
                            timeOff.fetchTimeOff()
                          ])
                        } catch (error) {
                          console.error('Failed to add time off:', error)
                          toast.error('Failed to add time off')
                        }
                      },
                      loading: false
                    })
                  }}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Time Off
                </Button>
              </div>
            </div>

            {timeOff.loading ? (
              <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-orange-600" />
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-medium text-gray-900">Loading Time Off</h4>
                  <p className="text-sm text-gray-600">Please wait while we fetch your time off data...</p>
                </div>
              </div>
            ) : timeOff.error ? (
              <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-medium text-gray-900">Error Loading Time Off</h4>
                  <p className="text-sm text-gray-600">{timeOff.error}</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      fetchTimeOff()
                      toast.info('Retrying...')
                    }}
                    className="mt-4"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <HolidayCalendar
                  holidays={[]}
                  onAddHoliday={handleAddHoliday}
                  onEditHoliday={handleEditHoliday}
                  onDeleteHoliday={handleDeleteHoliday}
                />

                {/* Bulk Operations for Time Off */}
                <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Plane className="h-5 w-5 text-orange-600" />
                      <span className="text-sm font-medium text-gray-900">Bulk Time Off Operations</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setConfirmDialog({
                            open: true,
                            title: 'Import Public Holidays',
                            description: 'Import Philippine public holidays for the current year. This will add standard holidays to your calendar.',
                            onConfirm: async () => {
                              // Implement public holiday import
                              toast.success('Public holidays imported successfully')
                            },
                            loading: false
                          })
                        }}
                      >
                        <Calendar className="h-4 w-4 mr-2" />
                        Import Holidays
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setConfirmDialog({
                            open: true,
                            title: 'Clear All Time Off',
                            description: 'Are you sure you want to remove all time off entries? This action cannot be undone.',
                            onConfirm: async () => {
                              // Implement bulk delete
                              toast.success('All time off entries cleared')
                            },
                            loading: false
                          })
                        }}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        Clear All
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Appointment Impact Warning */}
                <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-900 mb-1">Appointment Impact</h4>
                      <p className="text-sm text-yellow-700 mb-3">
                        Adding time off will automatically check for conflicting appointments and notify affected patients.
                      </p>
                      <div className="flex items-center gap-4 text-xs">
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-green-700">No conflicts</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                          <span className="text-yellow-700">2 appointments need rescheduling</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="settings" className="p-6 space-y-6">
            {/* Settings Header */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Schedule Settings</h3>
                <p className="text-sm text-gray-600">
                  Configure your schedule preferences and default settings
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Reset to default settings
                    setConfirmDialog({
                      open: true,
                      title: 'Reset to Defaults',
                      description: 'Are you sure you want to reset all schedule settings to their default values? This action cannot be undone.',
                      onConfirm: async () => {
                        toast.success('Settings reset to defaults')
                      },
                      loading: false
                    })
                  }}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reset Defaults
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    toast.success('Settings saved successfully')
                  }}
                  className="bg-gray-600 hover:bg-gray-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </Button>
              </div>
            </div>

            {scheduleTemplates.loading ? (
              <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-600" />
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-medium text-gray-900">Loading Settings</h4>
                  <p className="text-sm text-gray-600">Please wait while we fetch your settings...</p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Default Working Hours */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-blue-600" />
                      Default Working Hours
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      Set your standard working hours for new schedule templates
                    </p>
                  </CardHeader>
                  <CardContent>
                    <TimeSlotPicker
                      title="Standard Hours"
                      description="Your typical working day"
                      allowCustomTitle={false}
                      mode="form"
                      showActions={true}
                      loading={workingHours.loading}
                      onSave={async (data) => {
                        if (!dentistProfile?.id) {
                          console.error('Dentist profile not loaded')
                          return
                        }
                        await workingHours.createWorkingHour({
                          dentistId: dentistProfile.id,
                          dayOfWeek: new Date().getDay(),
                          startTime: data.startTime,
                          endTime: data.endTime,
                        })
                      }}
                    />
                  </CardContent>
                </Card>

                {/* Advanced Schedule Preferences */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5 text-gray-600" />
                      Advanced Preferences
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      Configure advanced schedule management options
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Timer className="h-4 w-4 text-blue-600" />
                          <div>
                            <p className="text-sm font-medium">Appointment Buffer Time</p>
                            <p className="text-xs text-gray-600">Time between appointments</p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-blue-700 border-blue-200">15 min</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Clock className="h-4 w-4 text-green-600" />
                          <div>
                            <p className="text-sm font-medium">Break Duration</p>
                            <p className="text-xs text-gray-600">Default break length</p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-green-700 border-green-200">30 min</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Calendar className="h-4 w-4 text-purple-600" />
                          <div>
                            <p className="text-sm font-medium">Booking Window</p>
                            <p className="text-xs text-gray-600">How far ahead patients can book</p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-purple-700 border-purple-200">30 days</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <AlertCircle className="h-4 w-4 text-red-600" />
                          <div>
                            <p className="text-sm font-medium">Emergency Slots</p>
                            <p className="text-xs text-gray-600">Reserved emergency appointments</p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-red-700 border-red-200">2 per day</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Notification Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="h-5 w-5 text-yellow-600" />
                      Notification Settings
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      Configure schedule-related notifications
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">Schedule Conflicts</p>
                          <p className="text-xs text-gray-600">Alert when conflicts are detected</p>
                        </div>
                        <Badge variant="default" className="bg-green-600">Enabled</Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">Appointment Reminders</p>
                          <p className="text-xs text-gray-600">Send reminders to patients</p>
                        </div>
                        <Badge variant="default" className="bg-green-600">Enabled</Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">Schedule Changes</p>
                          <p className="text-xs text-gray-600">Notify when schedule is modified</p>
                        </div>
                        <Badge variant="outline" className="text-gray-600">Disabled</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Integration Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Link className="h-5 w-5 text-indigo-600" />
                      Integration Settings
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      Configure external calendar and system integrations
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-indigo-50 rounded-lg border border-indigo-200">
                        <div className="flex items-center gap-3">
                          <Calendar className="h-4 w-4 text-indigo-600" />
                          <div>
                            <p className="text-sm font-medium text-indigo-900">Google Calendar</p>
                            <p className="text-xs text-indigo-700">Sync appointments with Google Calendar</p>
                          </div>
                        </div>
                        <Badge variant="default" className="bg-indigo-600">Connected</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Calendar className="h-4 w-4 text-gray-600" />
                          <div>
                            <p className="text-sm font-medium">Outlook Calendar</p>
                            <p className="text-xs text-gray-600">Sync with Microsoft Outlook</p>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">Connect</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Enhanced Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={async () => {
          setConfirmDialog(prev => ({ ...prev, loading: true }))
          try {
            await confirmDialog.onConfirm()
            setConfirmDialog(prev => ({ ...prev, open: false, loading: false }))
          } catch (error) {
            console.error('Confirmation action failed:', error)
            toast.error('Action failed. Please try again.')
            setConfirmDialog(prev => ({ ...prev, loading: false }))
          }
        }}
        loading={confirmDialog.loading}
        variant="destructive"
        confirmText="Confirm"
        cancelText="Cancel"
      />

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        richColors
        closeButton
        duration={4000}
        toastOptions={{
          style: {
            background: 'white',
            border: '1px solid #e5e7eb',
            color: '#374151',
          },
        }}
      />
    </div>
  )
}
