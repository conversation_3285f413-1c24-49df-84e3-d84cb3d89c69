'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import { formatTimeForDisplay } from '@/lib/validations/schedule'
import { Clock, Plus, Edit, Trash2, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'

interface TimeBlock {
  id: string
  title: string
  startTime: string
  endTime: string
  date: Date
  type: 'procedure' | 'break' | 'personal' | 'emergency' | 'admin'
  description?: string
  recurring?: boolean
  color?: string
}

interface TimeBlockingInterfaceProps {
  timeBlocks?: TimeBlock[]
  selectedDate?: Date
  onAddTimeBlock?: (timeBlock: Omit<TimeBlock, 'id'>) => void
  onEditTimeBlock?: (timeBlock: TimeBlock) => void
  onDeleteTimeBlock?: (timeBlockId: string) => void
  className?: string
  readOnly?: boolean
}

const TIME_BLOCK_TYPES = [
  { value: 'procedure', label: 'Procedure Time', color: 'bg-blue-100 text-blue-800 border-blue-300' },
  { value: 'break', label: 'Break Time', color: 'bg-green-100 text-green-800 border-green-300' },
  { value: 'personal', label: 'Personal Time', color: 'bg-purple-100 text-purple-800 border-purple-300' },
  { value: 'emergency', label: 'Emergency Buffer', color: 'bg-red-100 text-red-800 border-red-300' },
  { value: 'admin', label: 'Administrative', color: 'bg-yellow-100 text-yellow-800 border-yellow-300' }
]

const TIME_OPTIONS = Array.from({ length: 48 }, (_, i) => {
  const hour = Math.floor(i / 2) + 6 // Start from 6 AM
  const minute = i % 2 === 0 ? '00' : '30'
  if (hour >= 24) return null
  return `${hour.toString().padStart(2, '0')}:${minute}`
}).filter(Boolean) as string[]

export function TimeBlockingInterface({
  timeBlocks = [],
  selectedDate = new Date(),
  onAddTimeBlock,
  onEditTimeBlock,
  onDeleteTimeBlock,
  className,
  readOnly = false
}: TimeBlockingInterfaceProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = React.useState(false)
  const [editingBlock, setEditingBlock] = React.useState<TimeBlock | null>(null)
  const [newTimeBlock, setNewTimeBlock] = React.useState({
    title: '',
    startTime: '09:00',
    endTime: '10:00',
    type: 'procedure' as TimeBlock['type'],
    description: '',
    recurring: false
  })

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    }).format(date)
  }

  const getTimeBlocksForDate = () => {
    return timeBlocks
      .filter(block => block.date.toDateString() === selectedDate.toDateString())
      .sort((a, b) => a.startTime.localeCompare(b.startTime))
  }

  const getTypeConfig = (type: TimeBlock['type']) => {
    return TIME_BLOCK_TYPES.find(t => t.value === type) || TIME_BLOCK_TYPES[0]
  }

  const validateTimeBlock = (startTime: string, endTime: string, excludeId?: string) => {
    const [startHour, startMinute] = startTime.split(':').map(Number)
    const [endHour, endMinute] = endTime.split(':').map(Number)
    
    const startMinutes = startHour * 60 + startMinute
    const endMinutes = endHour * 60 + endMinute
    
    if (endMinutes <= startMinutes) {
      return { valid: false, message: 'End time must be after start time' }
    }

    // Check for overlaps with existing blocks
    const existingBlocks = getTimeBlocksForDate().filter(block => block.id !== excludeId)
    
    for (const block of existingBlocks) {
      const [blockStartHour, blockStartMinute] = block.startTime.split(':').map(Number)
      const [blockEndHour, blockEndMinute] = block.endTime.split(':').map(Number)
      
      const blockStartMinutes = blockStartHour * 60 + blockStartMinute
      const blockEndMinutes = blockEndHour * 60 + blockEndMinute
      
      // Check for overlap
      if (
        (startMinutes < blockEndMinutes && endMinutes > blockStartMinutes)
      ) {
        return { 
          valid: false, 
          message: `Overlaps with "${block.title}" (${block.startTime} - ${block.endTime})` 
        }
      }
    }

    return { valid: true, message: '' }
  }

  const handleAddTimeBlock = () => {
    const validation = validateTimeBlock(newTimeBlock.startTime, newTimeBlock.endTime)
    
    if (!validation.valid) {
      toast.error(validation.message)
      return
    }

    if (newTimeBlock.title) {
      onAddTimeBlock?.({
        title: newTimeBlock.title,
        startTime: newTimeBlock.startTime,
        endTime: newTimeBlock.endTime,
        date: selectedDate,
        type: newTimeBlock.type,
        description: newTimeBlock.description,
        recurring: newTimeBlock.recurring
      })
      
      setNewTimeBlock({
        title: '',
        startTime: '09:00',
        endTime: '10:00',
        type: 'procedure',
        description: '',
        recurring: false
      })
      setIsAddDialogOpen(false)
    }
  }

  const handleEditTimeBlock = (block: TimeBlock) => {
    setEditingBlock(block)
    setNewTimeBlock({
      title: block.title,
      startTime: block.startTime,
      endTime: block.endTime,
      type: block.type,
      description: block.description || '',
      recurring: block.recurring || false
    })
  }

  const handleUpdateTimeBlock = () => {
    if (!editingBlock) return

    const validation = validateTimeBlock(newTimeBlock.startTime, newTimeBlock.endTime, editingBlock.id)
    
    if (!validation.valid) {
      toast.error(validation.message)
      return
    }

    onEditTimeBlock?.({
      ...editingBlock,
      title: newTimeBlock.title,
      startTime: newTimeBlock.startTime,
      endTime: newTimeBlock.endTime,
      type: newTimeBlock.type,
      description: newTimeBlock.description,
      recurring: newTimeBlock.recurring
    })

    setEditingBlock(null)
    setNewTimeBlock({
      title: '',
      startTime: '09:00',
      endTime: '10:00',
      type: 'procedure',
      description: '',
      recurring: false
    })
  }

  const dayTimeBlocks = getTimeBlocksForDate()

  return (
    <Card className={cn('bg-white border-blue-200 shadow-lg', className)}>
      <CardHeader className="pb-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg text-gray-900 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-blue-600" />
            Time Blocks - {formatDate(selectedDate)}
          </CardTitle>
          
          {!readOnly && (
            <Dialog 
              open={isAddDialogOpen || !!editingBlock} 
              onOpenChange={(open) => {
                if (!open) {
                  setIsAddDialogOpen(false)
                  setEditingBlock(null)
                }
              }}
            >
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={() => setIsAddDialogOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Time Block
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>
                    {editingBlock ? 'Edit Time Block' : 'Add New Time Block'}
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="block-title">Title</Label>
                    <Input
                      id="block-title"
                      value={newTimeBlock.title}
                      onChange={(e) => setNewTimeBlock(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Enter time block title"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="start-time">Start Time</Label>
                      <Select 
                        value={newTimeBlock.startTime} 
                        onValueChange={(value) => setNewTimeBlock(prev => ({ ...prev, startTime: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TIME_OPTIONS.map((time) => (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="end-time">End Time</Label>
                      <Select 
                        value={newTimeBlock.endTime} 
                        onValueChange={(value) => setNewTimeBlock(prev => ({ ...prev, endTime: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TIME_OPTIONS.map((time) => (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="block-type">Type</Label>
                    <Select 
                      value={newTimeBlock.type} 
                      onValueChange={(value: TimeBlock['type']) => setNewTimeBlock(prev => ({ ...prev, type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {TIME_BLOCK_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="block-description">Description (Optional)</Label>
                    <Textarea
                      id="block-description"
                      value={newTimeBlock.description}
                      onChange={(e) => setNewTimeBlock(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter description"
                      rows={3}
                    />
                  </div>
                  
                  <div className="flex justify-end space-x-2">
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setIsAddDialogOpen(false)
                        setEditingBlock(null)
                      }}
                    >
                      Cancel
                    </Button>
                    <Button onClick={editingBlock ? handleUpdateTimeBlock : handleAddTimeBlock}>
                      {editingBlock ? 'Update' : 'Add'} Time Block
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-6">
        {dayTimeBlocks.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Time Blocks
            </h3>
            <p className="text-gray-600 mb-4">
              No time blocks scheduled for this date.
            </p>
            {!readOnly && (
              <Button
                onClick={() => setIsAddDialogOpen(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Time Block
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {dayTimeBlocks.map((block) => {
              const typeConfig = getTypeConfig(block.type)
              
              return (
                <div
                  key={block.id}
                  className={cn(
                    'p-4 rounded-lg border transition-all duration-200 hover:shadow-md',
                    typeConfig.color
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-medium">{block.title}</h4>
                        <Badge variant="outline" className="text-xs">
                          {typeConfig.label}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm mb-2">
                        <span className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {formatTimeForDisplay(block.startTime)} - {formatTimeForDisplay(block.endTime)}
                        </span>
                        {block.recurring && (
                          <span className="flex items-center text-blue-600">
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Recurring
                          </span>
                        )}
                      </div>
                      
                      {block.description && (
                        <p className="text-sm opacity-80">{block.description}</p>
                      )}
                    </div>
                    
                    {!readOnly && (
                      <div className="flex space-x-2 ml-4">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditTimeBlock(block)}
                          className="h-8 w-8 p-0 hover:bg-white/80"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onDeleteTimeBlock?.(block.id)}
                          className="h-8 w-8 p-0 hover:bg-white/80 text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
