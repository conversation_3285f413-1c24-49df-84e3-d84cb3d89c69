import { toast } from 'sonner'

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Custom error class for better error handling
export class AppError extends Error {
  public readonly type: ErrorType
  public readonly severity: ErrorSeverity
  public readonly userMessage: string
  public readonly retryable: boolean
  public readonly statusCode?: number

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    userMessage?: string,
    retryable: boolean = false,
    statusCode?: number
  ) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.severity = severity
    this.userMessage = userMessage || this.getDefaultUserMessage(type)
    this.retryable = retryable
    this.statusCode = statusCode
  }

  private getDefaultUserMessage(type: ErrorType): string {
    switch (type) {
      case ErrorType.NETWORK:
        return 'Network connection issue. Please check your internet connection and try again.'
      case ErrorType.VALIDATION:
        return 'Please check your input and try again.'
      case ErrorType.AUTHENTICATION:
        return 'Please sign in to continue.'
      case ErrorType.AUTHORIZATION:
        return 'You do not have permission to perform this action.'
      case ErrorType.NOT_FOUND:
        return 'The requested resource was not found.'
      case ErrorType.SERVER:
        return 'Server error occurred. Please try again later.'
      default:
        return 'An unexpected error occurred. Please try again.'
    }
  }
}

// Parse API errors and convert to AppError
export function parseApiError(error: any): AppError {
  // Handle fetch errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return new AppError(
      'Network request failed',
      ErrorType.NETWORK,
      ErrorSeverity.HIGH,
      'Unable to connect to the server. Please check your internet connection.',
      true
    )
  }

  // Handle HTTP response errors
  if (error.status) {
    const statusCode = error.status
    
    switch (statusCode) {
      case 400:
        return new AppError(
          'Bad request',
          ErrorType.VALIDATION,
          ErrorSeverity.MEDIUM,
          'Invalid request. Please check your input.',
          false,
          statusCode
        )
      case 401:
        return new AppError(
          'Unauthorized',
          ErrorType.AUTHENTICATION,
          ErrorSeverity.HIGH,
          'Your session has expired. Please sign in again.',
          false,
          statusCode
        )
      case 403:
        return new AppError(
          'Forbidden',
          ErrorType.AUTHORIZATION,
          ErrorSeverity.HIGH,
          'You do not have permission to perform this action.',
          false,
          statusCode
        )
      case 404:
        return new AppError(
          'Not found',
          ErrorType.NOT_FOUND,
          ErrorSeverity.MEDIUM,
          'The requested resource was not found.',
          false,
          statusCode
        )
      case 429:
        return new AppError(
          'Too many requests',
          ErrorType.SERVER,
          ErrorSeverity.MEDIUM,
          'Too many requests. Please wait a moment and try again.',
          true,
          statusCode
        )
      case 500:
      case 502:
      case 503:
      case 504:
        return new AppError(
          'Server error',
          ErrorType.SERVER,
          ErrorSeverity.HIGH,
          'Server error occurred. Please try again in a few moments.',
          true,
          statusCode
        )
      default:
        return new AppError(
          `HTTP ${statusCode} error`,
          ErrorType.SERVER,
          ErrorSeverity.MEDIUM,
          'An error occurred while processing your request.',
          statusCode >= 500,
          statusCode
        )
    }
  }

  // Handle validation errors from Zod or similar
  if (error.name === 'ZodError' || error.issues) {
    const firstIssue = error.issues?.[0]
    const message = firstIssue ? `${firstIssue.path.join('.')}: ${firstIssue.message}` : 'Validation failed'
    return new AppError(
      message,
      ErrorType.VALIDATION,
      ErrorSeverity.MEDIUM,
      'Please check your input and try again.',
      false
    )
  }

  // Handle generic errors
  if (error instanceof Error) {
    return new AppError(
      error.message,
      ErrorType.UNKNOWN,
      ErrorSeverity.MEDIUM,
      'An unexpected error occurred. Please try again.',
      false
    )
  }

  // Handle unknown error types
  return new AppError(
    'Unknown error occurred',
    ErrorType.UNKNOWN,
    ErrorSeverity.MEDIUM,
    'An unexpected error occurred. Please try again.',
    false
  )
}

// Enhanced error handler with retry functionality
export async function handleApiError(
  error: any,
  context: string,
  retryFn?: () => Promise<void>,
  maxRetries: number = 2
): Promise<void> {
  const appError = parseApiError(error)
  
  // Log error for debugging
  console.error(`[${context}] ${appError.type}:`, {
    message: appError.message,
    severity: appError.severity,
    statusCode: appError.statusCode,
    retryable: appError.retryable,
    originalError: error
  })

  // Show user-friendly error message
  if (appError.severity === ErrorSeverity.CRITICAL) {
    toast.error(appError.userMessage, {
      duration: 10000,
      action: retryFn && appError.retryable ? {
        label: 'Retry',
        onClick: () => retryWithBackoff(retryFn, maxRetries)
      } : undefined
    })
  } else if (appError.severity === ErrorSeverity.HIGH) {
    toast.error(appError.userMessage, {
      duration: 8000,
      action: retryFn && appError.retryable ? {
        label: 'Retry',
        onClick: () => retryWithBackoff(retryFn, maxRetries)
      } : undefined
    })
  } else {
    toast.error(appError.userMessage, {
      duration: 5000,
      action: retryFn && appError.retryable ? {
        label: 'Retry',
        onClick: () => retryWithBackoff(retryFn, maxRetries)
      } : undefined
    })
  }

  // Handle specific error types
  if (appError.type === ErrorType.AUTHENTICATION) {
    // Redirect to login or refresh token
    setTimeout(() => {
      window.location.href = '/auth/signin'
    }, 2000)
  }
}

// Retry function with exponential backoff
async function retryWithBackoff(
  fn: () => Promise<void>,
  maxRetries: number,
  currentRetry: number = 0
): Promise<void> {
  try {
    await fn()
    toast.success('Operation completed successfully')
  } catch {
    if (currentRetry < maxRetries) {
      const delay = Math.pow(2, currentRetry) * 1000 // Exponential backoff
      toast.info(`Retrying in ${delay / 1000} seconds...`)
      
      setTimeout(() => {
        retryWithBackoff(fn, maxRetries, currentRetry + 1)
      }, delay)
    } else {
      toast.error('Maximum retry attempts reached. Please try again later.')
    }
  }
}

// Utility function for handling async operations with error handling
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: string,
  retryFn?: () => Promise<void>
): Promise<T | null> {
  try {
    return await operation()
  } catch (error) {
    await handleApiError(error, context, retryFn)
    return null
  }
}

// Success message helper
export function showSuccessMessage(message: string, duration: number = 3000): void {
  toast.success(message, { duration })
}

// Info message helper
export function showInfoMessage(message: string, duration: number = 4000): void {
  toast.info(message, { duration })
}

// Warning message helper
export function showWarningMessage(message: string, duration: number = 5000): void {
  toast.warning(message, { duration })
}
