import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { bulkScheduleUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// POST /api/dentist/schedule/bulk-update - Bulk schedule operations
export async function POST(request: NextRequest) {
  return withAuth(async (req, user) => {
    try {
      const body = await request.json()
      const validatedData = bulkScheduleUpdateSchema.parse(body)

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })

        if (!dentistProfile || dentistProfile.id !== validatedData.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to modify this schedule' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Verify dentist exists
      const dentist = await prisma.dentistProfile.findUnique({
        where: { id: validatedData.dentistId }
      })

      if (!dentist) {
        return NextResponse.json(
          { error: 'Dentist not found' },
          { status: 404 }
        )
      }

      let result: any = {}

      switch (validatedData.operation) {
        case 'apply_template':
          result = await applyTemplate(validatedData)
          break
        case 'copy_week':
          result = await copyWeek(validatedData)
          break
        case 'clear_schedule':
          result = await clearSchedule(validatedData)
          break
        default:
          return NextResponse.json(
            { error: 'Invalid operation' },
            { status: 400 }
          )
      }

      return NextResponse.json({
        success: true,
        data: result
      })

    } catch (error) {
      console.error('Error performing bulk schedule update:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to perform bulk schedule update' },
        { status: 500 }
      )
    }
  }, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])(request)
}

// Apply template to date range
async function applyTemplate(data: any) {
  if (!data.templateId || !data.dateRange) {
    throw new Error('Template ID and date range are required for apply_template operation')
  }

  // Get the template
  const template = await prisma.dentistScheduleTemplate.findUnique({
    where: { id: data.templateId }
  })

  if (!template) {
    throw new Error('Template not found')
  }

  if (template.dentistId !== data.dentistId) {
    throw new Error('Template does not belong to the specified dentist')
  }

  const templateData = template.templateData as any
  const startDate = new Date(data.dateRange.startDate)
  const endDate = new Date(data.dateRange.endDate)

  const createdWorkingHours: any[] = []
  const createdTimeBlocks: any[] = []

  // Apply working hours from template
  if (templateData.workingHours && Array.isArray(templateData.workingHours)) {
    for (const workingHour of templateData.workingHours) {
      // Create working hours for the date range
      const existingWorkingHours = await prisma.dentistWorkingHours.findFirst({
        where: {
          dentistId: data.dentistId,
          dayOfWeek: workingHour.dayOfWeek,
          effectiveFrom: { lte: endDate },
          OR: [
            { effectiveTo: null },
            { effectiveTo: { gte: startDate } }
          ]
        }
      })

      if (!existingWorkingHours) {
        const newWorkingHours = await prisma.dentistWorkingHours.create({
          data: {
            dentistId: data.dentistId,
            dayOfWeek: workingHour.dayOfWeek,
            startTime: workingHour.startTime,
            endTime: workingHour.endTime,
            lunchStart: workingHour.lunchStart,
            lunchEnd: workingHour.lunchEnd,
            breakSlots: workingHour.breakSlots || [],
            effectiveFrom: startDate,
            effectiveTo: endDate,
          }
        })
        createdWorkingHours.push(newWorkingHours)
      }
    }
  }

  // Apply time blocks from template
  if (templateData.timeBlocks && Array.isArray(templateData.timeBlocks)) {
    const currentDate = new Date(startDate)
    
    while (currentDate <= endDate) {
      const dayOfWeek = currentDate.getDay()
      
      // Find time blocks for this day of week
      const dayTimeBlocks = templateData.timeBlocks.filter((tb: any) => tb.dayOfWeek === dayOfWeek)
      
      for (const timeBlock of dayTimeBlocks) {
        const [startHour, startMin] = timeBlock.startTime.split(':').map(Number)
        const [endHour, endMin] = timeBlock.endTime.split(':').map(Number)
        
        const blockStartTime = new Date(currentDate)
        blockStartTime.setHours(startHour, startMin, 0, 0)
        
        const blockEndTime = new Date(currentDate)
        blockEndTime.setHours(endHour, endMin, 0, 0)

        const newTimeBlock = await prisma.dentistTimeBlock.create({
          data: {
            dentistId: data.dentistId,
            title: timeBlock.title,
            description: timeBlock.description,
            startTime: blockStartTime,
            endTime: blockEndTime,
            blockType: timeBlock.blockType,
            color: timeBlock.color,
          }
        })
        createdTimeBlocks.push(newTimeBlock)
      }
      
      currentDate.setDate(currentDate.getDate() + 1)
    }
  }

  return {
    operation: 'apply_template',
    templateName: template.name,
    createdWorkingHours: createdWorkingHours.length,
    createdTimeBlocks: createdTimeBlocks.length,
    dateRange: data.dateRange
  }
}

// Copy week schedule
async function copyWeek(data: any) {
  if (!data.sourceWeek || !data.targetWeek) {
    throw new Error('Source week and target week are required for copy_week operation')
  }

  const sourceStart = new Date(data.sourceWeek)
  const sourceEnd = new Date(sourceStart)
  sourceEnd.setDate(sourceEnd.getDate() + 6) // End of week

  const targetStart = new Date(data.targetWeek)
  const targetEnd = new Date(targetStart)
  targetEnd.setDate(targetEnd.getDate() + 6) // End of week

  // Get source week data
  const sourceWorkingHours = await prisma.dentistWorkingHours.findMany({
    where: {
      dentistId: data.dentistId,
      effectiveFrom: { lte: sourceEnd },
      OR: [
        { effectiveTo: null },
        { effectiveTo: { gte: sourceStart } }
      ]
    }
  })

  const sourceTimeBlocks = await prisma.dentistTimeBlock.findMany({
    where: {
      dentistId: data.dentistId,
      startTime: { gte: sourceStart },
      endTime: { lte: sourceEnd },
      isActive: true
    }
  })

  const copiedWorkingHours: any[] = []
  const copiedTimeBlocks: any[] = []

  // Copy working hours
  for (const workingHour of sourceWorkingHours) {
    const newWorkingHours = await prisma.dentistWorkingHours.create({
      data: {
        dentistId: data.dentistId,
        dayOfWeek: workingHour.dayOfWeek,
        startTime: workingHour.startTime,
        endTime: workingHour.endTime,
        lunchStart: workingHour.lunchStart,
        lunchEnd: workingHour.lunchEnd,
        breakSlots: workingHour.breakSlots || [],
        effectiveFrom: targetStart,
        effectiveTo: targetEnd,
      }
    })
    copiedWorkingHours.push(newWorkingHours)
  }

  // Copy time blocks
  for (const timeBlock of sourceTimeBlocks) {
    const daysDiff = Math.floor((targetStart.getTime() - sourceStart.getTime()) / (1000 * 60 * 60 * 24))
    
    const newStartTime = new Date(timeBlock.startTime)
    newStartTime.setDate(newStartTime.getDate() + daysDiff)
    
    const newEndTime = new Date(timeBlock.endTime)
    newEndTime.setDate(newEndTime.getDate() + daysDiff)

    const newTimeBlock = await prisma.dentistTimeBlock.create({
      data: {
        dentistId: data.dentistId,
        title: timeBlock.title,
        description: timeBlock.description,
        startTime: newStartTime,
        endTime: newEndTime,
        blockType: timeBlock.blockType,
        isRecurring: timeBlock.isRecurring,
        recurringPattern: timeBlock.recurringPattern,
        color: timeBlock.color,
      }
    })
    copiedTimeBlocks.push(newTimeBlock)
  }

  return {
    operation: 'copy_week',
    sourceWeek: data.sourceWeek,
    targetWeek: data.targetWeek,
    copiedWorkingHours: copiedWorkingHours.length,
    copiedTimeBlocks: copiedTimeBlocks.length
  }
}

// Clear schedule for date range
async function clearSchedule(data: any) {
  if (!data.dateRange) {
    throw new Error('Date range is required for clear_schedule operation')
  }

  const startDate = new Date(data.dateRange.startDate)
  const endDate = new Date(data.dateRange.endDate)

  // Soft delete working hours
  const deletedWorkingHours = await prisma.dentistWorkingHours.updateMany({
    where: {
      dentistId: data.dentistId,
      effectiveFrom: { lte: endDate },
      OR: [
        { effectiveTo: null },
        { effectiveTo: { gte: startDate } }
      ]
    },
    data: {
      isActive: false
    }
  })

  // Soft delete time blocks
  const deletedTimeBlocks = await prisma.dentistTimeBlock.updateMany({
    where: {
      dentistId: data.dentistId,
      startTime: { gte: startDate },
      endTime: { lte: endDate },
      isActive: true
    },
    data: {
      isActive: false
    }
  })

  return {
    operation: 'clear_schedule',
    dateRange: data.dateRange,
    deletedWorkingHours: deletedWorkingHours.count,
    deletedTimeBlocks: deletedTimeBlocks.count
  }
}
