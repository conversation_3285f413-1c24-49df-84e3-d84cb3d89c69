# 🔧 Schedule Management System - Technical Specifications

## Current Implementation Status

### ✅ Completed Components
- **UI Components**: 5 reusable schedule components with healthcare design
- **API Endpoints**: 10 RESTful endpoints with RBAC and validation
- **Database Schema**: 4 new Prisma models with proper relationships
- **Mock Integration**: All components render with sample data

### 🚨 Missing Implementation
- **Form Handling**: No React Hook Form integration
- **API Integration**: Console.log placeholders instead of real API calls
- **State Management**: No persistent state or optimistic updates
- **User Feedback**: No loading states, error handling, or notifications

## Required Dependencies

```bash
# Form handling and validation
npm install react-hook-form @hookform/resolvers

# Toast notifications
npm install sonner

# State management (if not using built-in React state)
npm install zustand

# Date handling utilities
npm install date-fns
```

## Component Enhancement Specifications

### 1. TimeSlotPicker Component Enhancement

**Current State**: Basic time selection UI without form integration
**Required Changes**: Add form handling and validation

```typescript
// Enhanced interface
interface TimeSlotPickerProps {
  value?: TimeSlot
  onChange?: (timeSlot: TimeSlot) => void
  onSave?: (timeSlot: TimeSlot) => Promise<void>  // NEW
  loading?: boolean                               // NEW
  error?: string                                  // NEW
  className?: string
  title?: string
  description?: string
  allowCustomTitle?: boolean
  minTime?: string
  maxTime?: string
  step?: number
}

// Required form integration
const form = useForm<TimeSlotFormData>({
  resolver: zodResolver(timeSlotSchema),
  defaultValues: value || {
    startTime: "09:00",
    endTime: "17:00",
    title: ""
  }
})
```

### 2. WeeklyScheduleGrid Component Enhancement

**Current State**: Display-only grid with console.log interactions
**Required Changes**: Add CRUD operations with API integration

```typescript
// Enhanced event handlers
interface WeeklyScheduleGridProps {
  weekStart: Date
  schedule: DaySchedule[]
  loading?: boolean                               // NEW
  onTimeSlotClick?: (timeSlot: TimeSlot, date: Date) => void
  onAddTimeSlot?: (date: Date, time: string) => Promise<void>     // ENHANCED
  onEditTimeSlot?: (timeSlot: TimeSlot) => Promise<void>          // ENHANCED
  onDeleteTimeSlot?: (timeSlot: TimeSlot) => Promise<void>        // ENHANCED
  onBulkUpdate?: (updates: BulkScheduleUpdate) => Promise<void>   // NEW
  className?: string
  readOnly?: boolean
}
```

### 3. TimeBlockingInterface Component Enhancement

**Current State**: Static display of time blocks
**Required Changes**: Full CRUD functionality with forms

```typescript
// Enhanced interface
interface TimeBlockingInterfaceProps {
  timeBlocks: TimeBlock[]
  selectedDate: Date
  loading?: boolean                               // NEW
  onAddTimeBlock?: (block: TimeBlock) => Promise<void>           // ENHANCED
  onEditTimeBlock?: (block: TimeBlock) => Promise<void>          // ENHANCED
  onDeleteTimeBlock?: (blockId: string) => Promise<void>         // ENHANCED
  onBulkDelete?: (blockIds: string[]) => Promise<void>           // NEW
}
```

## API Integration Specifications

### Custom Hooks Required

```typescript
// src/hooks/useScheduleAPI.ts

export function useWorkingHours() {
  const [data, setData] = useState<WorkingHour[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchWorkingHours = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/dentist/schedule/working-hours')
      const result = await response.json()
      setData(result)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const createWorkingHour = async (workingHour: CreateWorkingHourData) => {
    setLoading(true)
    try {
      const response = await fetch('/api/dentist/schedule/working-hours', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workingHour)
      })
      const result = await response.json()
      setData(prev => [...prev, result])
      return result
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const updateWorkingHour = async (id: string, updates: UpdateWorkingHourData) => {
    // Implementation
  }

  const deleteWorkingHour = async (id: string) => {
    // Implementation
  }

  return {
    data,
    loading,
    error,
    fetchWorkingHours,
    createWorkingHour,
    updateWorkingHour,
    deleteWorkingHour
  }
}

// Similar hooks for:
// - useTimeBlocks()
// - useTimeOff()
// - useScheduleTemplates()
// - useAvailability()
```

### State Management Pattern

```typescript
// src/store/scheduleStore.ts (using Zustand)

interface ScheduleStore {
  // State
  workingHours: WorkingHour[]
  timeBlocks: TimeBlock[]
  holidays: Holiday[]
  scheduleTemplates: ScheduleTemplate[]
  loading: boolean
  error: string | null
  
  // Actions
  setWorkingHours: (hours: WorkingHour[]) => void
  addWorkingHour: (hour: WorkingHour) => void
  updateWorkingHour: (id: string, updates: Partial<WorkingHour>) => void
  removeWorkingHour: (id: string) => void
  
  setTimeBlocks: (blocks: TimeBlock[]) => void
  addTimeBlock: (block: TimeBlock) => void
  updateTimeBlock: (id: string, updates: Partial<TimeBlock>) => void
  removeTimeBlock: (id: string) => void
  
  setHolidays: (holidays: Holiday[]) => void
  addHoliday: (holiday: Holiday) => void
  updateHoliday: (id: string, updates: Partial<Holiday>) => void
  removeHoliday: (id: string) => void
  
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
}

export const useScheduleStore = create<ScheduleStore>((set, get) => ({
  // Initial state
  workingHours: [],
  timeBlocks: [],
  holidays: [],
  scheduleTemplates: [],
  loading: false,
  error: null,
  
  // Actions implementation
  setWorkingHours: (hours) => set({ workingHours: hours }),
  addWorkingHour: (hour) => set(state => ({ 
    workingHours: [...state.workingHours, hour] 
  })),
  // ... other actions
}))
```

## Form Validation Schemas

```typescript
// src/lib/validations/schedule.ts

import { z } from 'zod'

export const timeSlotSchema = z.object({
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  description: z.string().optional()
}).refine(data => {
  const start = new Date(`2000-01-01T${data.startTime}:00`)
  const end = new Date(`2000-01-01T${data.endTime}:00`)
  return end > start
}, {
  message: 'End time must be after start time',
  path: ['endTime']
})

export const timeBlockSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  date: z.date(),
  type: z.enum(['procedure', 'break', 'personal', 'emergency', 'admin']),
  description: z.string().optional()
})

export const holidaySchema = z.object({
  name: z.string().min(1, 'Holiday name is required'),
  date: z.date(),
  type: z.enum(['national', 'clinic', 'personal']),
  description: z.string().optional()
})

export const recurringPatternSchema = z.object({
  type: z.enum(['daily', 'weekly', 'monthly', 'custom']),
  interval: z.number().min(1, 'Interval must be at least 1'),
  daysOfWeek: z.array(z.number().min(0).max(6)).optional(),
  dayOfMonth: z.number().min(1).max(31).optional(),
  endDate: z.date().optional(),
  occurrences: z.number().min(1).optional()
})
```

## User Experience Enhancements

### 1. Loading States

```typescript
// Loading skeleton for schedule grid
const ScheduleGridSkeleton = () => (
  <div className="grid grid-cols-8 gap-2">
    {Array.from({ length: 56 }).map((_, i) => (
      <Skeleton key={i} className="h-16 w-full" />
    ))}
  </div>
)

// Button loading state
<Button disabled={loading}>
  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
  Save Changes
</Button>
```

### 2. Toast Notifications

```typescript
// Success/error feedback
import { toast } from 'sonner'

const handleSave = async (data) => {
  try {
    await saveWorkingHours(data)
    toast.success('Working hours saved successfully')
  } catch (error) {
    toast.error('Failed to save working hours')
  }
}
```

### 3. Confirmation Dialogs

```typescript
// Confirmation dialog component
const ConfirmationDialog = ({ 
  open, 
  onOpenChange, 
  title, 
  description, 
  onConfirm,
  loading 
}) => (
  <AlertDialog open={open} onOpenChange={onOpenChange}>
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>{title}</AlertDialogTitle>
        <AlertDialogDescription>{description}</AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel>Cancel</AlertDialogCancel>
        <AlertDialogAction onClick={onConfirm} disabled={loading}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Confirm
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
)
```

## Implementation Priority Matrix

### Phase 1: Core Functionality (Week 1)
- [ ] Add React Hook Form to TimeSlotPicker
- [ ] Create useWorkingHours API hook
- [ ] Implement save/cancel buttons
- [ ] Add basic error handling
- [ ] Integrate toast notifications

### Phase 2: CRUD Operations (Week 2)
- [ ] Complete TimeBlockingInterface forms
- [ ] Implement HolidayCalendar CRUD
- [ ] Add confirmation dialogs
- [ ] Create loading states
- [ ] Add form validation feedback

### Phase 3: Advanced Features (Week 3)
- [ ] Implement RecurringPatternSelector
- [ ] Add bulk operations
- [ ] Create schedule templates
- [ ] Add optimistic updates
- [ ] Implement conflict detection

### Phase 4: Polish & Testing (Week 4)
- [ ] Add comprehensive error handling
- [ ] Implement undo functionality
- [ ] Add keyboard shortcuts
- [ ] Performance optimization
- [ ] Accessibility improvements

## Testing Requirements

### Unit Tests
- Form validation schemas
- API hook functionality
- Component rendering with different states
- Error handling scenarios

### Integration Tests
- Complete user workflows
- API endpoint integration
- State management updates
- Cross-component interactions

### E2E Tests
- Schedule creation workflow
- Time block management
- Holiday management
- Settings configuration

## Performance Considerations

### Optimization Strategies
- Lazy load schedule components
- Implement virtual scrolling for large datasets
- Use React.memo for expensive components
- Debounce API calls for real-time updates
- Cache frequently accessed data

### Bundle Size Management
- Code splitting for schedule features
- Tree shaking unused utilities
- Optimize component imports
- Minimize third-party dependencies
