import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { timeBlockUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/dentist/schedule/time-blocks/[id] - Get specific time block
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  try {
    const timeBlock = await prisma.dentistTimeBlock.findUnique({
      where: { id },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      if (!timeBlock) {
        return NextResponse.json(
          { error: 'Time block not found' },
          { status: 404 }
        )
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== timeBlock.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to view this time block' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      return NextResponse.json({
        success: true,
        data: timeBlock
      })

    } catch (error) {
      console.error('Error fetching time block:', error)
      return NextResponse.json(
        { error: 'Failed to fetch time block' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])

// PUT /api/dentist/schedule/time-blocks/[id] - Update time block
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  try {
    const body = await req.json()
    const validatedData = timeBlockUpdateSchema.parse(body)

    // Check if time block exists
    const existingTimeBlock = await prisma.dentistTimeBlock.findUnique({
      where: { id }
      })

      if (!existingTimeBlock) {
        return NextResponse.json(
          { error: 'Time block not found' },
          { status: 404 }
        )
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== existingTimeBlock.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to modify this time block' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Check for overlapping time blocks if time is being changed
      if (validatedData.startTime || validatedData.endTime) {
        const newStartTime = validatedData.startTime || existingTimeBlock.startTime
        const newEndTime = validatedData.endTime || existingTimeBlock.endTime

        const overlappingBlocks = await prisma.dentistTimeBlock.findMany({
          where: {
            dentistId: existingTimeBlock.dentistId,
            isActive: true,
            id: { not: id }, // Exclude current block
            OR: [
              {
                startTime: {
                  lt: newEndTime
                },
                endTime: {
                  gt: newStartTime
                }
              }
            ]
          }
        })

        if (overlappingBlocks.length > 0) {
          return NextResponse.json(
            { error: 'Time block overlaps with existing blocks' },
            { status: 409 }
          )
        }
      }

      // Update time block
      const updatedTimeBlock = await prisma.dentistTimeBlock.update({
        where: { id: id },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        },
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: updatedTimeBlock
      })

    } catch (error) {
      console.error('Error updating time block:', error)
      
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Invalid input data', details: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to update time block' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])

// DELETE /api/dentist/schedule/time-blocks/[id] - Delete time block
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
    try {
      // Check if time block exists
      const existingTimeBlock = await prisma.dentistTimeBlock.findUnique({
        where: { id: id }
      })

      if (!existingTimeBlock) {
        return NextResponse.json(
          { error: 'Time block not found' },
          { status: 404 }
        )
      }

      // Authorization check
      if (user.role === UserRole.DENTIST) {
        const dentistProfile = await prisma.dentistProfile.findUnique({
          where: { userId: user.id }
        })
        
        if (!dentistProfile || dentistProfile.id !== existingTimeBlock.dentistId) {
          return NextResponse.json(
            { error: 'Unauthorized to modify this time block' },
            { status: 403 }
          )
        }
      } else if (![UserRole.ADMIN as string, UserRole.STAFF as string].includes(user.role as string)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Soft delete by setting isActive to false
      await prisma.dentistTimeBlock.update({
        where: { id: id },
        data: {
          isActive: false,
          updatedAt: new Date(),
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Time block deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting time block:', error)
      return NextResponse.json(
        { error: 'Failed to delete time block' },
        { status: 500 }
      )
    }
}, [UserRole.DENTIST, UserRole.STAFF, UserRole.ADMIN])
