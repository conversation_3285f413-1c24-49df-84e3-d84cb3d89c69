'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { PatientList } from '@/components/patients/patient-list'
import { PatientStatisticsCards } from '@/components/patients/patient-statistics'
import { PatientDetailModal } from '@/components/patients/patient-detail-modal'
import { PatientEditModal } from '@/components/patients/patient-edit-modal'
import { PatientWithCounts } from '@/types/patient'
import { UserRole } from '@prisma/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import {
  Loader2,
  <PERSON>,
  RefreshCw,
  Filter,
  Search,
  Download,
  Upload,
  Plus,
  Edit,
  Trash2,
  Timer,
  FileText,
  Activity,
  Calendar,
  TrendingUp,
  Target,
  AlertCircle,
  Heart,
  Stethoscope
} from 'lucide-react'
import { toast } from 'sonner'

// Enhanced patient records state interface
interface PatientRecordsState {
  activeView: 'overview' | 'medical' | 'analytics'
  isRefreshing: boolean
  lastRefresh: Date
  hasError: boolean
  errorMessage?: string
  selectedPatients: string[]
  bulkActionMode: boolean
  searchQuery: string
  filterStatus: 'all' | 'active' | 'inactive' | 'new'
}

// Confirmation dialog state
interface ConfirmDialogState {
  open: boolean
  title: string
  description: string
  onConfirm: () => Promise<void>
  loading: boolean
  variant?: 'default' | 'destructive'
}

export default function DentistPatients() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [selectedPatient, setSelectedPatient] = useState<PatientWithCounts | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  // Enhanced state management
  const [patientState, setPatientState] = useState<PatientRecordsState>({
    activeView: 'overview',
    isRefreshing: false,
    lastRefresh: new Date(),
    hasError: false,
    selectedPatients: [],
    bulkActionMode: false,
    searchQuery: '',
    filterStatus: 'all'
  })

  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    open: false,
    title: '',
    description: '',
    onConfirm: async () => {},
    loading: false
  })

  // Enhanced refresh functionality
  const refreshPatientData = useCallback(async () => {
    setPatientState(prev => ({ ...prev, isRefreshing: true, hasError: false }))
    try {
      // Simulate API refresh
      await new Promise(resolve => setTimeout(resolve, 1000))
      setRefreshKey(prev => prev + 1)
      setPatientState(prev => ({
        ...prev,
        isRefreshing: false,
        lastRefresh: new Date()
      }))
      toast.success('Patient records refreshed successfully')
    } catch (_error) {
      setPatientState(prev => ({
        ...prev,
        isRefreshing: false,
        hasError: true,
        errorMessage: 'Failed to refresh patient data'
      }))
      toast.error('Failed to refresh patient records')
    }
  }, [])

  // Bulk operations handlers
  const handleBulkAction = useCallback((action: 'export' | 'archive' | 'delete' | 'message') => {
    if (patientState.selectedPatients.length === 0) {
      toast.error('Please select patients first')
      return
    }

    const actionMessages = {
      export: {
        title: 'Export Patient Records',
        description: `Export ${patientState.selectedPatients.length} patient record(s) to CSV format. This will include basic information and medical history.`,
        variant: 'default' as const
      },
      archive: {
        title: 'Archive Selected Patients',
        description: `Archive ${patientState.selectedPatients.length} patient record(s)? Archived patients will be moved to inactive status but can be restored later.`,
        variant: 'default' as const
      },
      delete: {
        title: 'Delete Selected Patients',
        description: `Are you sure you want to permanently delete ${patientState.selectedPatients.length} patient record(s)? This action cannot be undone and will remove all associated data.`,
        variant: 'destructive' as const
      },
      message: {
        title: 'Send Message to Patients',
        description: `Send a message or notification to ${patientState.selectedPatients.length} selected patient(s). This will open the messaging interface.`,
        variant: 'default' as const
      }
    }

    const config = actionMessages[action]
    setConfirmDialog({
      open: true,
      title: config.title,
      description: config.description,
      variant: config.variant,
      loading: false,
      onConfirm: async () => {
        // Implement bulk action logic
        await new Promise(resolve => setTimeout(resolve, 1500))
        toast.success(`${action} completed for ${patientState.selectedPatients.length} patients`)
        setPatientState(prev => ({
          ...prev,
          selectedPatients: [],
          bulkActionMode: false
        }))
      }
    })
  }, [patientState.selectedPatients])

  // Toggle bulk action mode
  const toggleBulkMode = useCallback(() => {
    setPatientState(prev => ({
      ...prev,
      bulkActionMode: !prev.bulkActionMode,
      selectedPatients: []
    }))
  }, [])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/patients')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  const handleViewPatient = (patient: PatientWithCounts) => {
    setSelectedPatient(patient)
    setIsDetailModalOpen(true)
  }

  const handleEditPatient = (patient: PatientWithCounts) => {
    setSelectedPatient(patient)
    setIsEditModalOpen(true)
  }

  const handleDeletePatient = (patient: PatientWithCounts) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Patient Record',
      description: `Are you sure you want to delete ${patient.user.name || 'this patient'}&apos;s record? This action cannot be undone and will remove all associated medical history, appointments, and treatment records.`,
      variant: 'destructive',
      loading: false,
      onConfirm: async () => {
        // Implement delete logic
        await new Promise(resolve => setTimeout(resolve, 1500))
        toast.success('Patient record deleted successfully')
        handleRefreshData()
      }
    })
  }

  const handleAddPatient = () => {
    router.push('/dentist/patients/new')
  }

  const handleRefreshData = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false)
    setSelectedPatient(null)
  }

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false)
    setSelectedPatient(null)
  }

  const handleEditFromDetail = (_patientId: string) => {
    setIsDetailModalOpen(false)
    setIsEditModalOpen(true)
  }

  const handleSavePatient = () => {
    handleRefreshData()
    setIsEditModalOpen(false)
    setSelectedPatient(null)
  }

  return (
    <div className="p-6 space-y-8">
      {/* Enhanced Header with Actions */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-transparent to-green-600/5 rounded-2xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-blue-100/50">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Patient Records</h1>
                  <p className="text-gray-600 font-medium">
                    Manage patient information, medical history, and treatment records
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Timer className="h-3 w-3" />
                  <span>Last updated: {patientState.lastRefresh.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'Asia/Manila'
                  })}</span>
                </div>
                {patientState.bulkActionMode && (
                  <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                    <Edit className="h-3 w-3 mr-1" />
                    Bulk Mode ({patientState.selectedPatients.length} selected)
                  </Badge>
                )}
                {patientState.searchQuery && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    <Search className="h-3 w-3 mr-1" />
                    Searching: &quot;{patientState.searchQuery}&quot;
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshPatientData}
                disabled={patientState.isRefreshing}
                className="hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
              >
                {patientState.isRefreshing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleBulkMode}
                className={`transition-all duration-200 ${
                  patientState.bulkActionMode
                    ? 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100'
                    : 'hover:bg-purple-50 hover:border-purple-200'
                }`}
              >
                <Edit className="h-4 w-4 mr-2" />
                {patientState.bulkActionMode ? 'Exit Bulk Mode' : 'Bulk Actions'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/appointments')}
                className="hover:bg-green-50 hover:border-green-200 transition-all duration-200"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Appointments
              </Button>
              <Button
                size="sm"
                onClick={handleAddPatient}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Patient
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions Toolbar */}
      {patientState.bulkActionMode && (
        <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Edit className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h3 className="font-medium text-purple-900">Bulk Actions Mode</h3>
                <p className="text-sm text-purple-700">
                  {patientState.selectedPatients.length} patient(s) selected
                </p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('export')}
                disabled={patientState.selectedPatients.length === 0}
                className="border-blue-200 text-blue-700 hover:bg-blue-50"
              >
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('message')}
                disabled={patientState.selectedPatients.length === 0}
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                <FileText className="h-3 w-3 mr-1" />
                Message
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('archive')}
                disabled={patientState.selectedPatients.length === 0}
                className="border-orange-200 text-orange-700 hover:bg-orange-50"
              >
                <AlertCircle className="h-3 w-3 mr-1" />
                Archive
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('delete')}
                disabled={patientState.selectedPatients.length === 0}
                className="border-red-200 text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Statistics Cards */}
      <PatientStatisticsCards key={refreshKey} />

      {/* Enhanced Patient Management with Tabs */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <Tabs value={patientState.activeView} onValueChange={(value) =>
          setPatientState(prev => ({ ...prev, activeView: value as PatientRecordsState['activeView'] }))
        } className="w-full">
          {/* Enhanced Tab Navigation */}
          <div className="border-b border-gray-200 bg-gradient-to-r from-blue-50/30 via-white to-green-50/30">
            <div className="px-6 py-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Patient Management</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Comprehensive patient records and medical history
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-blue-50 hover:border-blue-200"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Advanced Filter
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-green-50 hover:border-green-200"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-purple-50 hover:border-purple-200"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </Button>
                </div>
              </div>

              <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span className="hidden sm:inline">Overview</span>
                </TabsTrigger>
                <TabsTrigger value="medical" className="flex items-center gap-2">
                  <Stethoscope className="h-4 w-4" />
                  <span className="hidden sm:inline">Medical History</span>
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="hidden sm:inline">Analytics</span>
                </TabsTrigger>
              </TabsList>
            </div>
          </div>

          {/* Overview Tab - Main Patient List */}
          <TabsContent value="overview" className="p-6">
            <PatientList
              key={refreshKey}
              onViewPatient={handleViewPatient}
              onEditPatient={handleEditPatient}
              onDeletePatient={handleDeletePatient}
              onAddPatient={handleAddPatient}
            />
          </TabsContent>

          {/* Medical History Tab */}
          <TabsContent value="medical" className="p-6 space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="hover:shadow-lg transition-all duration-300 border-green-100/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="h-5 w-5 text-green-600" />
                    Medical History Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                      <div className="text-3xl font-bold text-green-600 mb-2">Medical Timeline</div>
                      <p className="text-sm text-green-700">Coming Soon</p>
                      <p className="text-xs text-green-600 mt-2">
                        Interactive timeline of patient medical history and treatments
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-blue-100/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-blue-600" />
                    Dental Charts & Imaging
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                      <div className="text-3xl font-bold text-blue-600 mb-2">Dental Charts</div>
                      <p className="text-sm text-blue-700">Coming Soon</p>
                      <p className="text-xs text-blue-600 mt-2">
                        Interactive dental charts with X-ray and imaging integration
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2 hover:shadow-lg transition-all duration-300 border-purple-100/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-purple-600" />
                    Insurance & Billing Integration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                      <div className="text-3xl font-bold text-purple-600 mb-2">Insurance Billing</div>
                      <p className="text-sm text-purple-700">Coming Soon</p>
                      <p className="text-xs text-purple-600 mt-2">
                        Integrated insurance claim processing and billing workflows
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="p-6 space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="hover:shadow-lg transition-all duration-300 border-orange-100/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-orange-600" />
                    Patient Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg">
                      <div className="text-3xl font-bold text-orange-600 mb-2">Patient Insights</div>
                      <p className="text-sm text-orange-700">Coming Soon</p>
                      <p className="text-xs text-orange-600 mt-2">
                        Demographics, treatment patterns, and patient satisfaction metrics
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-teal-100/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-teal-600" />
                    Treatment Outcomes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-6 bg-gradient-to-br from-teal-50 to-teal-100 rounded-lg">
                      <div className="text-3xl font-bold text-teal-600 mb-2">Treatment Analytics</div>
                      <p className="text-sm text-teal-700">Coming Soon</p>
                      <p className="text-xs text-teal-600 mt-2">
                        Success rates, treatment duration, and outcome analysis
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Patient Detail Modal */}
      <PatientDetailModal
        patientId={selectedPatient?.id || null}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        onEdit={handleEditFromDetail}
      />

      {/* Patient Edit Modal */}
      <PatientEditModal
        patientId={selectedPatient?.id || null}
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        onSave={handleSavePatient}
      />

      {/* Enhanced Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={async () => {
          setConfirmDialog(prev => ({ ...prev, loading: true }))
          try {
            await confirmDialog.onConfirm()
            setConfirmDialog(prev => ({ ...prev, open: false, loading: false }))
          } catch (error) {
            console.error('Confirmation action failed:', error)
            toast.error('Action failed. Please try again.')
            setConfirmDialog(prev => ({ ...prev, loading: false }))
          }
        }}
        loading={confirmDialog.loading}
        variant={confirmDialog.variant}
        confirmText="Continue"
        cancelText="Cancel"
      />
    </div>
  )
}
